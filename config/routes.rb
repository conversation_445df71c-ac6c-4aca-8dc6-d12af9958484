Rails.application.routes.draw do
  resources :resumes do
    resources :experiences, :educations, :projects, :skills, :languages, :references, :hobbies, :volunteerings, :awards, :certifications
  end
  resource :session
  resources :passwords, param: :token
  post "signup", to: "sessions#signup"
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Defines the root path route ("/")
  # root "posts#index"
end
