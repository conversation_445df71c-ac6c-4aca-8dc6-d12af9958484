# SQLite. Versions 3.8.0 and up are supported.
#   gem install sqlite3
#
#   Ensure the SQLite 3 gem is defined in your Gemfile
#   gem "sqlite3"
#
default: &default
  adapter: sqlite3
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>
  timeout: 5000

development:
  <<: *default
  database: storage/development.sqlite3

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  <<: *default
  database: storage/test.sqlite3


# Production database configuration for PostgreSQL with Coolify
production:
  primary: &production_primary
    adapter: postgresql
    encoding: unicode
    pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>
    database: <%= ENV["POSTGRES_DB"] || "quickcv_production" %>
    username: <%= ENV["POSTGRES_USER"] || "postgres" %>
    password: <%= ENV["POSTGRES_PASSWORD"] %>
    host: <%= ENV["POSTGRES_HOST"] || "db" %>
    port: <%= ENV["POSTGRES_PORT"] || 5432 %>
  cache:
    <<: *production_primary
    database: <%= ENV["POSTGRES_CACHE_DB"] || "quickcv_cache_production" %>
    migrations_paths: db/cache_migrate
  queue:
    <<: *production_primary
    database: <%= ENV["POSTGRES_QUEUE_DB"] || "quickcv_queue_production" %>
    migrations_paths: db/queue_migrate
  cable:
    <<: *production_primary
    database: <%= ENV["POSTGRES_CABLE_DB"] || "quickcv_cable_production" %>
    migrations_paths: db/cable_migrate
