"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";

// Color schemes
export const COLOR_SCHEMES = {
  blue: {
    id: "blue",
    name: "Professional Blue",
    primary: "#3B82F6",
    secondary: "#1E40AF",
    accent: "#60A5FA",
    text: "#1F2937",
    background: "#FFFFFF",
  },
  green: {
    id: "green",
    name: "<PERSON> Green",
    primary: "#10B981",
    secondary: "#047857",
    accent: "#34D399",
    text: "#1F2937",
    background: "#FFFFFF",
  },
  purple: {
    id: "purple",
    name: "<PERSON> Purple",
    primary: "#8B5CF6",
    secondary: "#7C3AED",
    accent: "#A78BFA",
    text: "#1F2937",
    background: "#FFFFFF",
  },
  red: {
    id: "red",
    name: "Bold Red",
    primary: "#EF4444",
    secondary: "#DC2626",
    accent: "#F87171",
    text: "#1F2937",
    background: "#FFFFFF",
  },
  orange: {
    id: "orange",
    name: "Energetic Orange",
    primary: "#F97316",
    secondary: "#EA580C",
    accent: "#FB923C",
    text: "#1F2937",
    background: "#FFFFFF",
  },
  gray: {
    id: "gray",
    name: "Classic Gray",
    primary: "#6B7280",
    secondary: "#4B5563",
    accent: "#9CA3AF",
    text: "#1F2937",
    background: "#FFFFFF",
  },
};

// Font families
export const FONT_FAMILIES = {
  inter: {
    id: "inter",
    name: "Inter",
    family: "Inter, sans-serif",
    category: "modern",
  },
  roboto: {
    id: "roboto",
    name: "Roboto",
    family: "Roboto, sans-serif",
    category: "modern",
  },
  "open-sans": {
    id: "open-sans",
    name: "Open Sans",
    family: "Open Sans, sans-serif",
    category: "modern",
  },
  lato: {
    id: "lato",
    name: "Lato",
    family: "Lato, sans-serif",
    category: "modern",
  },
  "times-new-roman": {
    id: "times-new-roman",
    name: "Times New Roman",
    family: "Times New Roman, serif",
    category: "traditional",
  },
  georgia: {
    id: "georgia",
    name: "Georgia",
    family: "Georgia, serif",
    category: "traditional",
  },
  playfair: {
    id: "playfair",
    name: "Playfair Display",
    family: "Playfair Display, serif",
    category: "elegant",
  },
};

// Layout options
export const LAYOUT_OPTIONS = {
  spacing: {
    compact: { id: "compact", name: "Compact", value: 0.8 },
    normal: { id: "normal", name: "Normal", value: 1.0 },
    relaxed: { id: "relaxed", name: "Relaxed", value: 1.2 },
  },
  margins: {
    narrow: { id: "narrow", name: "Narrow", value: 0.5 },
    normal: { id: "normal", name: "Normal", value: 0.75 },
    wide: { id: "wide", name: "Wide", value: 1.0 },
  },
};

// Customization state interface
export interface ResumeCustomization {
  colorScheme: keyof typeof COLOR_SCHEMES;
  fontFamily: keyof typeof FONT_FAMILIES;
  spacing: keyof typeof LAYOUT_OPTIONS.spacing;
  margins: keyof typeof LAYOUT_OPTIONS.margins;
  sectionOrder: string[];
}

// Default customization
const DEFAULT_CUSTOMIZATION: ResumeCustomization = {
  colorScheme: "blue",
  fontFamily: "inter",
  spacing: "normal",
  margins: "normal",
  sectionOrder: [
    "personal",
    "summary",
    "experience",
    "education",
    "skills",
    "languages",
    "certifications",
  ],
};

// Context interface
interface ResumeCustomizationContextType {
  customization: ResumeCustomization;
  updateCustomization: (updates: Partial<ResumeCustomization>) => void;
  resetCustomization: () => void;
  getColorScheme: () => typeof COLOR_SCHEMES[keyof typeof COLOR_SCHEMES];
  getFontFamily: () => typeof FONT_FAMILIES[keyof typeof FONT_FAMILIES];
  getSpacing: () => typeof LAYOUT_OPTIONS.spacing[keyof typeof LAYOUT_OPTIONS.spacing];
  getMargins: () => typeof LAYOUT_OPTIONS.margins[keyof typeof LAYOUT_OPTIONS.margins];
}

// Create context
const ResumeCustomizationContext = createContext<ResumeCustomizationContextType | undefined>(
  undefined
);

// Provider component
export const ResumeCustomizationProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [customization, setCustomization] = useState<ResumeCustomization>(
    DEFAULT_CUSTOMIZATION
  );

  const updateCustomization = (updates: Partial<ResumeCustomization>) => {
    setCustomization((prev) => ({ ...prev, ...updates }));
  };

  const resetCustomization = () => {
    setCustomization(DEFAULT_CUSTOMIZATION);
  };

  const getColorScheme = () => COLOR_SCHEMES[customization.colorScheme];
  const getFontFamily = () => FONT_FAMILIES[customization.fontFamily];
  const getSpacing = () => LAYOUT_OPTIONS.spacing[customization.spacing];
  const getMargins = () => LAYOUT_OPTIONS.margins[customization.margins];

  const value: ResumeCustomizationContextType = {
    customization,
    updateCustomization,
    resetCustomization,
    getColorScheme,
    getFontFamily,
    getSpacing,
    getMargins,
  };

  return (
    <ResumeCustomizationContext.Provider value={value}>
      {children}
    </ResumeCustomizationContext.Provider>
  );
};

// Hook to use the context
export const useResumeCustomization = () => {
  const context = useContext(ResumeCustomizationContext);
  if (context === undefined) {
    throw new Error(
      "useResumeCustomization must be used within a ResumeCustomizationProvider"
    );
  }
  return context;
};
