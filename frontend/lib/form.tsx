import { Input } from "@heroui/input";
import { Checkbox } from "@heroui/react";

import { routes } from "@/config/path-constants";
import {
  awardSchema,
  certificationSchema,
  educationSchema,
  experienceSchema,
  hobbySchema,
  languageSchema,
  projectSchema,
  referenceSchema,
  skillSchema,
  volunteeringSchema,
} from "@/config/schemas";
import { Resume } from "@/types/resume";
import { TrixEditorField } from "@/components/TrixEditorField";

export const renderField = <T extends Record<string, any>>(
  property: string,
  schemaProperty: any,
  index: number,
  item: T,
  schema: string,
) => {
  const { type, label, placeholder, className } = schemaProperty;
  let inputElement = null;

  switch (type) {
    case "string":
      inputElement = (
        <Input
          key={property}
          className={className}
          defaultValue={item[property]}
          name={`resume[${schema}_attributes][${index}][${property}]`}
          placeholder={placeholder}
        />
      );
      break;
    case "number":
      inputElement = (
        <Input
          key={property}
          className={className}
          defaultValue={item[property]}
          name={`resume[${schema}_attributes][${index}][${property}]`}
          placeholder={placeholder}
          type="number"
        />
      );
      break;
    case "boolean":
      inputElement = (
        <div className="flex items-center space-x-2 col-span-full">
          <Checkbox
            defaultChecked={item[property]}
            name={`resume[${schema}_attributes][${index}][${property}]`}
          >
            {label}
          </Checkbox>
        </div>
      );
      break;
    case "textarea":
      inputElement = (
        <TrixEditorField
          key={property}
          className="col-span-full"
          id={`${schema}_${index}_${property}`}
          label={label}
          name={`${schema}[${index}].${property}`}
          value={item[property] || ""}
        />
      );
      break;
    default:
      return null;
  }

  return inputElement;
};

export const RenderForm = ({
  schema,
  item,
  index,
}: {
  schema: any;
  item: any;
  index: number;
}) => {
  return (
    <>
      {item && item.id && (
        <>
          <Input
            defaultValue={item.id}
            name={`resume[${schema.collection}_attributes][${index}][id]`}
            type="hidden"
          />
          <Input
            defaultValue={item.sort}
            name={`resume[${schema.collection}_attributes][${index}][sort]`}
            type="hidden"
          />
        </>
      )}
      {Object.entries(schema.properties).map(([property, schemaProperty]) =>
        renderField(property, schemaProperty, index, item, schema.collection),
      )}
    </>
  );
};

export const prepareNestedForms = (resume: Resume) => {
  return [
    {
      name: "education",
      keyName: "institution",
      schema: educationSchema,
      route: routes.add_education_url,
      baseRoute: routes.educations_url,
      items: resume.educations,
    },
    {
      name: "experience",
      keyName: "company",
      schema: experienceSchema,
      route: routes.add_experience_url,
      baseRoute: routes.experiences_url,
      items: resume.experiences,
    },
    {
      name: "project",
      keyName: "title",
      schema: projectSchema,
      route: routes.add_project_url,
      baseRoute: routes.projects_url,
      items: resume.projects,
    },
    {
      name: "award",
      keyName: "title",
      schema: awardSchema,
      route: routes.add_award_url,
      baseRoute: routes.awards_url,
      items: resume.awards,
    },
    {
      name: "certification",
      keyName: "title",
      schema: certificationSchema,
      route: routes.add_certification_url,
      baseRoute: routes.certifications_url,
      items: resume.certifications,
    },
    {
      name: "skill",
      keyName: "name",
      schema: skillSchema,
      route: routes.add_skill_url,
      baseRoute: routes.skills_url,
      items: resume.skills,
    },
    {
      name: "language",
      keyName: "name",
      schema: languageSchema,
      route: routes.add_language_url,
      baseRoute: routes.languages_url,
      items: resume.languages,
      layout: "simple",
    },
    {
      name: "reference",
      keyName: "name",
      schema: referenceSchema,
      route: routes.add_reference_url,
      baseRoute: routes.references_url,
      items: resume.references,
    },
    {
      name: "hobby",
      keyName: "name",
      schema: hobbySchema,
      route: routes.add_hobby_url,
      baseRoute: routes.hobbies_url,
      items: resume.hobbies,
      layout: "simple",
    },
    {
      name: "volunteering",
      keyName: "role",
      schema: volunteeringSchema,
      route: routes.add_volunteering_url,
      baseRoute: routes.volunteerings_url,
      items: resume.volunteerings,
    },
  ];
};
