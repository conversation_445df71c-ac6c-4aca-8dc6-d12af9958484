"use server";
import { cookies } from "next/headers";
import { apiClient } from "./api";

export async function createSession(token: string) {
  try {
    // Store JWT in a cookie
    const cookieStore = await cookies();

    cookieStore.set({
      name: "auth_token",
      value: token,
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      maxAge: 60 * 60 * 24 * 7, // 1 week
      path: "/",
      sameSite: "lax",
    });

    return true;
  } catch (error) {
    return false;
  }
}

export async function isAuthenticated(): Promise<boolean> {
  try {
    const response = await apiClient<{ authenticated: boolean }>("/session", {
      method: "GET",
    });
    // Ensure response and response.data exist and authenticated is true
    return !!(response && response.data && response.data.authenticated);
  } catch (error: any) {
    // If the API call for /session results in a 401, it means the user is not authenticated.
    // For other errors, or if authenticated is explicitly false, also treat as not authenticated.
    if (error && error.status === 401) {
      // This is an expected "unauthenticated" state, no need to log as an error here.
      // The Navbar will simply show guest links.
      return false;
    }
    // For unexpected errors during the authentication check, log them.
    return false;
  }
}
