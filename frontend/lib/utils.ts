export const createEmptyResume = (title: string) => {
  return {
    title: title || "",
    first_name: "",
    last_name: "",
    job_title: "",
    address: "",
    email: "",
    website: "",
    bio: "",
    birth_date: "",
    city: "",
    street: "",
    country: "",
    show_photo: false,
    photo_url: "",
    photo: null,
  };
};

export const getInitials = (firstName?: string, lastName?: string) => {
  const first = firstName?.charAt(0) || "";
  const last = lastName?.charAt(0) || "";
  return (first + last).toUpperCase();
};

export const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat("en-US", {
    day: "numeric",
    month: "short",
    year: "numeric",
  }).format(date);
};

export const upperCaseFirstLetter = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};
