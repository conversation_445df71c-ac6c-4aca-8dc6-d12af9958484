import { Resume } from "@/types/resume";

/**
 * Sample resume data for testing templates
 * This provides realistic data to showcase template features
 */
export const createSampleResumeData = (): Resume => {
  return {
    id: 1,
    title: "Senior Software Engineer Resume",
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    job_title: "Senior Software Engineer",
    address: "123 Tech Street, Apt 4B",
    email: "<EMAIL>",
    website: "https://sarah<PERSON>hnson.dev",
    bio: "Experienced Senior Software Engineer with 8+ years of expertise in full-stack development, cloud architecture, and team leadership. Proven track record of delivering scalable solutions and mentoring junior developers. Passionate about clean code, agile methodologies, and emerging technologies.",
    birth_date: "1990-03-15",
    city: "San Francisco",
    street: "123 Tech Street",
    country: "USA",
    show_photo: true,
    photo:
      "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    template_id: 1,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-15T00:00:00Z",

    experiences: [
      {
        id: 1,
        company: "TechCorp Solutions",
        title: "Senior Software Engineer",
        city: "San Francisco",
        country: "USA",
        start_date: "2021-06-01",
        end_date: "",
        is_current: true,
        description:
          "Lead development of microservices architecture serving 2M+ users daily. Mentor team of 5 junior developers and conduct code reviews. Implemented CI/CD pipelines reducing deployment time by 60%. Technologies: React, Node.js, AWS, Docker, Kubernetes.",
      },
      {
        id: 2,
        company: "StartupXYZ",
        title: "Full Stack Developer",
        city: "San Francisco",
        country: "USA",
        start_date: "2019-03-01",
        end_date: "2021-05-31",
        is_current: false,
        description:
          "Built and maintained customer-facing web applications using React and Python. Collaborated with product team to define technical requirements. Optimized database queries improving application performance by 40%. Participated in agile development process.",
      },
      {
        id: 3,
        company: "Digital Agency Pro",
        title: "Junior Software Developer",
        city: "Oakland",
        country: "USA",
        start_date: "2017-08-01",
        end_date: "2019-02-28",
        is_current: false,
        description:
          "Developed responsive websites and web applications for clients. Worked with HTML, CSS, JavaScript, and PHP. Collaborated with designers to implement pixel-perfect UI components. Maintained and updated existing client websites.",
      },
    ],

    educations: [
      {
        id: 1,
        city: "Berkeley",
        country: "USA",
        institution: "University of California, Berkeley",
        degree: "Bachelor of Science",
        field_of_study: "Computer Science",
        website: "https://berkeley.edu",
        is_current: false,
        start_date: "2013-09-01",
        end_date: "2017-05-31",
        description:
          "Relevant coursework: Data Structures, Algorithms, Software Engineering, Database Systems, Computer Networks. GPA: 3.7/4.0",
      },
    ],

    projects: [
      {
        id: 1,
        title: "E-commerce Platform",
        client: "Personal Project",
        start_date: "2023-01-01",
        end_date: "2023-06-30",
        description:
          "Built a full-stack e-commerce platform with React, Node.js, and PostgreSQL. Features include user authentication, payment processing, inventory management, and admin dashboard. Deployed on AWS with auto-scaling capabilities.",
        url: "https://github.com/sarahjohnson/ecommerce-platform",
      },
      {
        id: 2,
        title: "Task Management API",
        client: "Open Source",
        start_date: "2022-09-01",
        end_date: "2022-12-31",
        description:
          "Developed RESTful API for task management application using Express.js and MongoDB. Implemented JWT authentication, real-time notifications, and comprehensive testing suite. Documentation available on GitHub.",
        url: "https://github.com/sarahjohnson/task-api",
      },
    ],

    skills: [
      {
        id: 1,
        name: "JavaScript",
        proficiency: 95,
        category: "Programming Languages",
        resume_id: "1",
      },
      {
        id: 2,
        name: "TypeScript",
        proficiency: 90,
        category: "Programming Languages",
        resume_id: "1",
      },
      {
        id: 3,
        name: "Python",
        proficiency: 85,
        category: "Programming Languages",
        resume_id: "1",
      },
      {
        id: 4,
        name: "React",
        proficiency: 95,
        category: "Frontend",
        resume_id: "1",
      },
      {
        id: 5,
        name: "Node.js",
        proficiency: 90,
        category: "Backend",
        resume_id: "1",
      },
      {
        id: 6,
        name: "Express.js",
        proficiency: 88,
        category: "Backend",
        resume_id: "1",
      },
      {
        id: 7,
        name: "PostgreSQL",
        proficiency: 85,
        category: "Databases",
        resume_id: "1",
      },
      {
        id: 8,
        name: "MongoDB",
        proficiency: 80,
        category: "Databases",
        resume_id: "1",
      },
      {
        id: 9,
        name: "AWS",
        proficiency: 85,
        category: "Cloud",
        resume_id: "1",
      },
      {
        id: 10,
        name: "Docker",
        proficiency: 80,
        category: "DevOps",
        resume_id: "1",
      },
      {
        id: 11,
        name: "Kubernetes",
        proficiency: 75,
        category: "DevOps",
        resume_id: "1",
      },
      {
        id: 12,
        name: "Git",
        proficiency: 95,
        category: "Tools",
        resume_id: "1",
      },
    ],

    certifications: [
      {
        id: 1,
        title: "AWS Certified Solutions Architect",
        issuer: "Amazon Web Services",
        date_obtained: "2022-08-15",
        expiration_date: "2025-08-15",
        description:
          "Professional-level certification demonstrating expertise in designing distributed systems on AWS.",
        resume_id: "1",
      },
      {
        id: 2,
        title: "Certified Kubernetes Administrator",
        issuer: "Cloud Native Computing Foundation",
        date_obtained: "2023-03-20",
        expiration_date: "2026-03-20",
        description:
          "Validates skills in Kubernetes cluster administration and troubleshooting.",
        resume_id: "1",
      },
    ],

    awards: [
      {
        id: 1,
        title: "Employee of the Year",
        issuer: "TechCorp Solutions",
        date_recieved: "2023-12-01",
        description:
          "Recognized for outstanding performance and leadership in delivering critical projects.",
        resume_id: "1",
      },
      {
        id: 2,
        title: "Hackathon Winner",
        issuer: "Bay Area Tech Meetup",
        date_recieved: "2022-10-15",
        description:
          "First place in 48-hour hackathon for developing innovative AI-powered solution.",
        resume_id: "1",
      },
    ],

    languages: [
      { id: 1, name: "English", proficiency: 100, resume_id: "1" },
      { id: 2, name: "Spanish", proficiency: 75, resume_id: "1" },
      { id: 3, name: "French", proficiency: 60, resume_id: "1" },
    ],

    references: [
      {
        id: 1,
        name: "Michael Chen",
        company: "TechCorp Solutions",
        position: "Engineering Manager",
        email: "<EMAIL>",
        phone: "+****************",
        description:
          "Direct supervisor for 2+ years. Can speak to technical skills and leadership abilities.",
        resume_id: "1",
      },
      {
        id: 2,
        name: "Lisa Rodriguez",
        company: "StartupXYZ",
        position: "CTO",
        email: "<EMAIL>",
        phone: "+****************",
        description:
          "Former CTO who worked closely on product development and architecture decisions.",
        resume_id: "1",
      },
    ],

    hobbies: [
      { id: 1, name: "Rock Climbing" },
      { id: 2, name: "Photography" },
      { id: 3, name: "Open Source Contributing" },
      { id: 4, name: "Cooking" },
    ],

    volunteerings: [
      {
        id: 1,
        organization: "Code for Good",
        role: "Volunteer Developer",
        start_date: "2020-01-01",
        end_date: "",
        description:
          "Volunteer with local non-profit to build web applications for community organizations. Led development of donation tracking system used by 5+ local charities.",
      },
      {
        id: 2,
        organization: "Girls Who Code",
        role: "Mentor",
        start_date: "2021-09-01",
        end_date: "2023-06-30",
        description:
          "Mentored high school students learning programming fundamentals. Conducted weekly coding sessions and career guidance workshops.",
      },
    ],
  };
};

// Alternative sample data for different industries
export const createMarketingResumeData = (): Resume => {
  const base = createSampleResumeData();
  return {
    ...base,
    first_name: "Alex",
    last_name: "Martinez",
    job_title: "Digital Marketing Manager",
    bio: "Creative Digital Marketing Manager with 6+ years of experience driving brand growth through data-driven campaigns. Expert in social media marketing, content strategy, and marketing automation. Proven track record of increasing engagement by 200% and generating $2M+ in revenue.",
    experiences: [
      {
        id: 1,
        company: "Creative Agency Inc",
        title: "Digital Marketing Manager",
        city: "Los Angeles",
        country: "USA",
        start_date: "2021-01-01",
        end_date: "",
        is_current: true,
        description:
          "Lead digital marketing campaigns for 15+ clients across various industries. Manage social media accounts with 500K+ combined followers. Developed content strategy increasing engagement by 200%. Oversee marketing automation workflows and email campaigns.",
      },
    ],
    skills: [
      {
        id: 1,
        name: "Google Analytics",
        proficiency: 95,
        category: "Analytics",
        resume_id: "1",
      },
      {
        id: 2,
        name: "Facebook Ads",
        proficiency: 90,
        category: "Paid Advertising",
        resume_id: "1",
      },
      {
        id: 3,
        name: "Content Strategy",
        proficiency: 95,
        category: "Strategy",
        resume_id: "1",
      },
      {
        id: 4,
        name: "SEO",
        proficiency: 85,
        category: "Digital Marketing",
        resume_id: "1",
      },
      {
        id: 5,
        name: "Email Marketing",
        proficiency: 90,
        category: "Digital Marketing",
        resume_id: "1",
      },
    ],
  };
};

export default createSampleResumeData;
