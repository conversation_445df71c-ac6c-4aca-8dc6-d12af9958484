import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { BASE_URL } from "@/app/constants";

export type ApiOptions = {
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  headers?: Record<string, string>;
  body?: any;
};

interface ApiError {
  status: number;
  statusText: string;
  error: any;
  message: string;
}

interface ApiResponse<T> {
  success: boolean;
  data: T;
}

async function getTokenFromCookies(): Promise<string | undefined> {
  return (await cookies()).get("auth_token")?.value;
}

export async function apiClient<T>(
  url: string,
  options: ApiOptions = {},
): Promise<ApiResponse<T>> {
  const { method = "GET", headers = {}, body } = options;

  const token = await getTokenFromCookies();

  const fetchHeaders: HeadersInit = {
    ...headers,
  };

  // Don't set Content-Type for FormData, browser does it
  if (!(body instanceof FormData)) {
    fetchHeaders["Content-Type"] = "application/json";
  }

  if (token) {
    fetchHeaders["Authorization"] = `Bearer ${token}`;
  }

  const res = await fetch(`${BASE_URL}${url}`, {
    method,
    headers: fetchHeaders,
    body:
      body instanceof FormData ? body : body ? JSON.stringify(body) : undefined,
    credentials: "include",
  });

  if (!res.ok) {
    const error = await res.json().catch(() => ({}));
    const errorData: ApiError = {
      status: res.status,
      statusText: res.statusText,
      error: error,
      message: error.error || "An error occurred",
    };

    // Conditionally redirect
    if (errorData.status === 401 && url !== "/session") {
      redirect("/login");
    }

    throw errorData;
  }

  const success = res.ok;

  if (res.status === 204) {
    return { success, data: {} as T };
  }

  const data = await res.json();

  return { success, data };
}
