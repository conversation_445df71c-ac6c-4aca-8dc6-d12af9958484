"use client";

import { Input, Link } from "@heroui/react";
import { Icon } from "@iconify/react";
import React, { useActionState } from "react";
import { signUp } from "@/actions/auth";
import SubmitButton from "@/components/submit-button";
import { useActionErrors } from "@/hooks/use-action-errors";

export default function Component() {
  const [isVisible, setIsVisible] = React.useState(false);
  const toggleVisibility = () => setIsVisible(!isVisible);

  const [state, action] = useActionState(signUp, {
    errors: {
      fieldErrors: {},
      formErrors: [],
    },
  });

  const { errors, setFieldError } = useActionErrors(state);

  const fieldErrors = errors?.fieldErrors || {};
  const formErrors: string[] | undefined = errors?.formErrors;

  return (
    <div className="flex h-full w-full items-center justify-center">
      <div className="flex w-full max-w-sm flex-col gap-4 rounded-large px-8 pb-10 pt-6">
        {formErrors && (
          <div className="mb-4">
            {formErrors.map((error, index) => (
              <p key={index} className="text-red-500">
                {error}
              </p>
            ))}
          </div>
        )}
        <p className="pb-4 text-left text-3xl font-semibold">
          Sign Up
          <span aria-label="emoji" className="ml-2" role="img">
            👋
          </span>
        </p>
        <form action={action} className="flex flex-col gap-4">
          <Input
            isRequired
            errorMessage={fieldErrors.email_address}
            isInvalid={!!fieldErrors.email_address}
            label="Email"
            labelPlacement="outside"
            name="email_address"
            placeholder="Enter your email"
            type="email"
            variant="bordered"
            onChange={() => setFieldError("email_address")}
          />
          <Input
            isRequired
            endContent={
              <button type="button" onClick={toggleVisibility}>
                {isVisible ? (
                  <Icon
                    className="pointer-events-none text-2xl text-default-400"
                    icon="solar:eye-closed-linear"
                  />
                ) : (
                  <Icon
                    className="pointer-events-none text-2xl text-default-400"
                    icon="solar:eye-bold"
                  />
                )}
              </button>
            }
            errorMessage={fieldErrors.password}
            isInvalid={!!fieldErrors.password}
            label="Password"
            labelPlacement="outside"
            name="password"
            placeholder="Enter your password"
            type={isVisible ? "text" : "password"}
            variant="bordered"
            onChange={() => setFieldError("password")}
          />

          <SubmitButton>Sign Up</SubmitButton>
        </form>
        <p className="text-center text-small">
          <Link href="/login" size="sm">
            Already have an account? Log In
          </Link>
        </p>
      </div>
    </div>
  );
}
