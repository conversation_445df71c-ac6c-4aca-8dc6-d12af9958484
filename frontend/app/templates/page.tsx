"use client";

import React, { useState } from "react";
import {
  getAllTemplates,
  Template<PERSON>enderer,
  getTemplatesByCategory,
} from "@/components/resume/templates";
import {
  createSampleResumeData,
  createMarketingResumeData,
} from "@/lib/sample-resume-data";
import { TemplateGallery } from "@/components/templates/TemplateGallery";

export default function TemplatesPage() {
  const [selectedTemplate, setSelectedTemplate] = useState("classic");
  const [selectedCategory, setSelectedCategory] = useState<
    "all" | "professional" | "creative" | "minimal" | "modern"
  >("all");
  const [sampleDataType, setSampleDataType] = useState<"tech" | "marketing">(
    "tech",
  );

  const allTemplates = getAllTemplates();
  const currentTemplate = allTemplates.find((t) => t.id === selectedTemplate);
  const sampleResume =
    sampleDataType === "tech"
      ? createSampleResumeData()
      : createMarketingResumeData();

  const filteredTemplates =
    selectedCategory === "all"
      ? allTemplates
      : getTemplatesByCategory(selectedCategory);

  return (
    <div className="templates-showcase max-w-7xl mx-auto p-6 grid grid-cols-1 lg:grid-cols-4 gap-6">
      <div className="lg:col-span-1">
        <h1 className="text-3xl font-bold mb-4">Resume Templates</h1>
        <p className="text-gray-600 mb-6">
          Choose from our collection of ATS-friendly resume templates. All
          templates are optimized for applicant tracking systems while
          maintaining professional appearance.
        </p>
        <TemplateGallery
          sampleResume={sampleResume}
          selectedTemplate={selectedTemplate}
          setSelectedTemplate={setSelectedTemplate}
          templates={filteredTemplates}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Template Preview */}
        <div className="lg:col-span-3">
          <div className="preview-container bg-gray-100 p-6 rounded-lg">
            <div
              className="bg-white shadow-lg mx-auto"
              style={{ width: "8.5in", minHeight: "11in" }}
            >
              <TemplateRenderer
                className="w-full h-full"
                resume={sampleResume}
                templateId={selectedTemplate}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
