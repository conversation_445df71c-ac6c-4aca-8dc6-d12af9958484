import { getResume } from "@/actions/resume";
import { ResumeEditForm } from "@/components/resume/resume-edit-form";
import { ResumePreview } from "@/components/resume/resume-preview";

export default async function EditResumePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params; // Destructure id from params
  const resumeId = Number(id);
  const resume = await getResume(resumeId);

  if (!resume) {
    return <div>Resume not found</div>;
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 h-full">
      <div className="order-2 lg:order-1 h-full">
        <ResumePreview
          className="h-full"
          resume={resume}
          showControls={true}
          showTemplateSelector={true}
        />
      </div>
      <div className="order-1 lg:order-2 w-full py-4 overflow-auto">
        <ResumeEditForm data={resume} />
      </div>
    </div>
  );
}
