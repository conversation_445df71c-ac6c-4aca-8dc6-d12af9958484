import { listResumes } from "@/actions/resume";
import ResumeCard from "@/components/resume/resume-card";

export default async function ResumesPage() {
  const resumes = await listResumes();

  if (!resumes) {
    return <div className="text-center">Resumes not found</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {resumes.map((resume) => (
        <ResumeCard key={resume.id} resume={resume} />
      ))}
    </div>
  );
}
