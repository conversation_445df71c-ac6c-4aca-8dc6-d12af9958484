"use client";

import {
  <PERSON><PERSON>,
  <PERSON>dal,
  <PERSON>dal<PERSON>ody,
  Modal<PERSON>ontent,
  Spacer,
  useDisclosure,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { AcmeIcon } from "@/components/acem";
import { TemplateGallery } from "@/components/templates/TemplateGallery";
import { getAllTemplates } from "@/components/resume/templates";

/**
 * 💡 TIP: You can use the usePathname hook from Next.js App Router to get the current pathname
 * and use it as the active key for the Sidebar component.
 *
 * ```tsx
 * import {usePathname} from "next/navigation";
 *
 * const pathname = usePathname();
 * const currentPath = pathname.split("/")?.[1]
 *
 * <Sidebar defaultSelectedKey="home" selectedKeys={[currentPath]} />
 * ```
 */
export default function Component({ children }: { children: React.ReactNode }) {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const sidebarWidth = 288;

  const allTemplates = getAllTemplates();

  return (
    <div className="flex h-dvh w-full">
      <Modal
        classNames={{
          base: "justify-start sm:m-0 p-0 h-dvh max-h-full w-[var(--sidebar-width)]",
          wrapper: "items-start justify-start !w-[var(--sidebar-width)]",
          body: "p-0",
          closeButton: "z-50",
        }}
        isOpen={isOpen}
        motionProps={{
          variants: {
            enter: {
              x: 0,
              transition: {
                duration: 0.3,
                ease: "easeOut",
              },
            },
            exit: {
              x: -288,
              transition: {
                duration: 0.2,
                ease: "easeOut",
              },
            },
          },
        }}
        radius="none"
        scrollBehavior="inside"
        style={{
          // @ts-ignore
          "--sidebar-width": `${sidebarWidth}px`,
        }}
        onOpenChange={onOpenChange}
      >
        <ModalContent>
          <ModalBody>
            <div className="relative flex h-full w-72 flex-1 flex-col p-6">
              <div className="flex items-center gap-2 px-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-foreground">
                  <AcmeIcon className="text-background" />
                </div>
                <span className="text-small font-bold uppercase text-foreground">
                  Acme
                </span>
              </div>
              <Spacer y={8} />

              <div className="overflow-y-auto overflow-x-hidden h-full max-h-screen grid grid-cols-1 gap-4">
                <TemplateGallery
                  selectedTemplate="classic"
                  setSelectedTemplate={() => {}}
                  templates={allTemplates}
                />
              </div>

              <Spacer y={8} />
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
      <div className="w-full flex flex-1 p-4 gap-2 ">
        <Button isIconOnly size="sm" variant="light" onPress={onOpen}>
          <Icon
            className="text-default-500"
            height={24}
            icon="solar:hamburger-menu-outline"
            width={24}
          />
        </Button>
        <main className="mt-4 h-full w-full overflow-visible">
          <div className="flex h-[90%] w-full flex-col gap-4 rounded-medium">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
