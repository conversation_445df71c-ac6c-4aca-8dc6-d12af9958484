export interface Resume {
  id: number;
  title: string;
  first_name: string;
  last_name: string;
  job_title: string;
  address: string;
  email: string;
  website: string;
  bio: { body: string };
  birth_date: string;
  city: string;
  street: string;
  country: string;
  show_photo: boolean;
  photo: string;
  template_id: number;
  educations: EducationType[];
  experiences: ExperienceType[];
  projects: ProjectType[];
  awards: AwardType[];
  certifications: CertificationType[];
  skills: SkillType[];
  languages: LanguageType[];
  references: ReferenceType[];
  hobbies: HobbyType[];
  volunteerings: VolunteeringType[];
  created_at: string;
  updated_at: string;
}

export interface EducationType {
  id: number;
  city: string;
  country: string;
  institution: string;
  degree: string;
  field_of_study: string;
  website: string;
  is_current: boolean;
  start_date: string;
  end_date: string;
  description: string;
}

export interface ExperienceType {
  id: number;
  company: string;
  title: string;
  city: string;
  country: string;
  start_date: string;
  end_date: string;
  is_current: boolean;
  description: string;
  resume_id?: string; // Optional as it might not be present on new records
  // For Rails nested attributes
}

export interface ProjectType {
  id: number;
  title: string;
  client: string;
  start_date: string;
  end_date: string;
  description: string;
  url: string;
}

export interface AwardType {
  id: number;
  title: string;
  issuer: string;
  date_recieved: string;
  description: string;
  resume_id: string;
}

export interface CertificationType {
  id: number;
  title: string;
  issuer: string;
  date_obtained: string;
  expiration_date: string;
  description: string;
  resume_id: string;
}

export interface SkillType {
  id: number;
  name: string;
  proficiency: number;
  category: string;
  resume_id: string;
}

export interface LanguageType {
  id: number;
  name: string;
  proficiency: number;
  resume_id: string;
}

export interface ReferenceType {
  id: number;
  name: string;
  company: string;
  position: string;
  email: string;
  phone: string;
  description: string;
  resume_id: string;
}

export interface HobbyType {
  id?: number;
  name: string;
}

export interface VolunteeringType {
  id?: number;
  organization: string;
  role: string;
  start_date?: string; // Dates can be optional and will be strings from the form
  end_date?: string;
  description?: string;
}
