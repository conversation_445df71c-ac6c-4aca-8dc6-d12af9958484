// submit-button.tsx
"use client";

import { Button } from "@heroui/react";
import { useFormStatus } from "react-dom";

export default function SubmitButton({
  children,
  className,
  size,
}: {
  children?: React.ReactNode;
  className?: string;
  size?: "lg" | "md" | "sm";
}) {
  const { pending } = useFormStatus();
  return (
    <Button
      className={className}
      color="primary"
      disabled={pending}
      fullWidth={true}
      isLoading={pending}
      size={size}
      type="submit"
    >
      {children}
    </Button>
  );
}
