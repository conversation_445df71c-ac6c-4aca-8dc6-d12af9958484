import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Divider } from "@heroui/react";
import { Icon } from "@iconify/react";
import { ReactNode } from "react";

interface FormSectionProps {
  onAddNew?: () => void;
  addButtonText?: string;
  children: ReactNode;
  className?: string;
}

export default function FormSection({
  onAddNew,
  addButtonText = "Add New",
  children,
  className,
}: FormSectionProps) {
  return (
    <Card className={`w-full ${className || ""}`}>
      <CardHeader className="flex w-full items-center justify-end">
        {onAddNew && (
          <Button
            color="primary"
            startContent={<Icon icon="lucide:plus" />}
            variant="flat"
            onPress={onAddNew}
          >
            {addButtonText}
          </Button>
        )}
      </CardHeader>
      <Divider />
      <CardBody className="px-6 py-4">{children}</CardBody>
    </Card>
  );
}
