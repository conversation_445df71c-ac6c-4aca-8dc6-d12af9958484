"use client";

import { DatePicker } from "@heroui/react";
import { DateValue, parseDate } from "@internationalized/date";
import React from "react";

export default function DatePickerComponent({
  className,
  defaultValue,
  label,
  name,
}: {
  className?: string;
  defaultValue?: string;
  label?: string;
  name?: string;
}) {
  const [value, setValue] = React.useState<DateValue | null | undefined>(
    defaultValue
      ? (parseDate(defaultValue) as unknown as DateValue)
      : undefined,
  );

  return (
    <DatePicker
      className={className}
      label={label}
      name={name}
      value={value as any}
      onChange={(newValue) => setValue(newValue as any)}
    />
  );
}
