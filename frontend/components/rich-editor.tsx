import { Button } from "@heroui/react";
import { cn } from "@heroui/react";
import Placeholder from "@tiptap/extension-placeholder";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { Icon } from "@iconify/react";
import { useEffect, useState } from "react";

interface RichEditorProps {
  value: string;
  placeholder?: string;
  className?: string;
  minHeight?: string;
  readOnly?: boolean;
  id?: string; // Add this
  name?: string; // Add this
}

export function RichEditor({
  value,
  name,
  placeholder = "Write something...",
  className,
  minHeight = "200px",
  readOnly = false,
  id,
}: RichEditorProps) {
  const [content, setContent] = useState(value);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
      }),
      Placeholder.configure({
        placeholder,
      }),
    ],
    content: value,
    editable: !readOnly,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      setContent(html);
    },
  });

  // Update editor content when value prop changes
  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value || "");
    }
  }, [value, editor]);

  if (!editor) {
    return null;
  }

  return (
    <div
      className={cn("rounded-md border border-input bg-background", className)}
    >
      {!readOnly && (
        <div className="flex flex-wrap items-center gap-1 border-b border-input bg-muted p-1">
          <Button
            className={editor.isActive("bold") ? "bg-accent" : ""}
            size="sm"
            title="Bold"
            type="button"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleBold().run()}
          >
            <Icon height={16} icon="solar:text-bold-linear" width={16} />
          </Button>
          <Button
            className={editor.isActive("italic") ? "bg-accent" : ""}
            size="sm"
            title="Italic"
            type="button"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleItalic().run()}
          >
            <Icon height={16} icon="solar:text-italic-linear" width={16} />
          </Button>
          <div className="h-6 w-px bg-border" />
          <Button
            className={editor.isActive("bulletList") ? "bg-accent" : ""}
            size="sm"
            title="Bullet List"
            type="button"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleBulletList().run()}
          >
            <Icon height={16} icon="solar:list-linear" width={16} />
          </Button>
          <Button
            className={editor.isActive("orderedList") ? "bg-accent" : ""}
            size="sm"
            title="Ordered List"
            type="button"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
          >
            <Icon height={16} icon="solar:list-1-linear" width={16} />
          </Button>
          <div className="h-6 w-px bg-border" />
          <div className="flex-1" />
          <Button
            disabled={!editor.can().undo()}
            size="sm"
            title="Undo"
            type="button"
            variant="ghost"
            onClick={() => editor.chain().focus().undo().run()}
          >
            <Icon height={16} icon="solar:undo-left-linear" width={16} />
          </Button>
          <Button
            disabled={!editor.can().redo()}
            size="sm"
            title="Redo"
            type="button"
            variant="ghost"
            onClick={() => editor.chain().focus().redo().run()}
          >
            <Icon height={16} icon="solar:undo-right-linear" width={16} />
          </Button>
        </div>
      )}

      <div
        className="prose prose-sm max-w-none p-4 focus:outline-none"
        style={{ minHeight }}
      >
        <EditorContent editor={editor} id={id} />
        {/* Hidden input to include content in form submission */}
        <input name={name} type="hidden" value={content || ""} />
      </div>
    </div>
  );
}
