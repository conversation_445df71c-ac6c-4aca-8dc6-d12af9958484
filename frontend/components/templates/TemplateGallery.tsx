"use client";

import { <PERSON>, CardB<PERSON>, Chip } from "@heroui/react";

import { ATSCompatibilityIndicator } from "@/components/resume/templates/template-registry";

interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  atsScore: number;
  features: string[];
}

interface TemplateGalleryProps {
  templates: Template[];

  selectedTemplate: string;
  setSelectedTemplate: (templateId: string) => void;
}

export const TemplateGallery: React.FC<TemplateGalleryProps> = ({
  templates,
  setSelectedTemplate,
  selectedTemplate,
}) => {
  return (
    <>
      {/* Template List */}
      <div className="col-span-1">
        <h2 className="text-xl font-semibold mb-4">Templates</h2>
        <div className="space-y-3">
          {templates.map((template) => (
            <Card
              key={template.id}
              isPressable
              className={`cursor-pointer transition-all ${
                selectedTemplate === template.id
                  ? "border-2 border-blue-500 bg-blue-50"
                  : "border border-gray-200 hover:border-gray-300"
              }`}
              onPress={() => setSelectedTemplate(template.id)}
            >
              <CardBody className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-sm">{template.name}</h3>
                  <Chip
                    color={
                      template.category === "professional"
                        ? "primary"
                        : template.category === "creative"
                          ? "secondary"
                          : template.category === "modern"
                            ? "success"
                            : "default"
                    }
                    size="sm"
                    variant="flat"
                  >
                    {template.category}
                  </Chip>
                </div>
                <p className="text-xs text-gray-600 mb-3">
                  {template.description}
                </p>
                <ATSCompatibilityIndicator score={template.atsScore} />

                {/* Features */}
                <div className="mt-3">
                  <p className="text-xs font-medium text-gray-700 mb-1">
                    Features:
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {template.features.slice(0, 3).map((feature, index) => (
                      <span
                        key={index}
                        className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded"
                      >
                        {feature}
                      </span>
                    ))}
                    {template.features.length > 3 && (
                      <span className="text-xs text-gray-500">
                        +{template.features.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    </>
  );
};
