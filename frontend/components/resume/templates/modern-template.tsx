import React from "react";
import {
  TemplateProps,
  ResumeHeader,
  ProfessionalSummary,
  ExperienceSection,
  EducationSection,
  SkillsSection,
} from "./base-components";
import { TwoColumnLayout } from "./additional-sections";
import {
  ProjectsSection,
  CertificationsSection,
  AwardsSection,
  LanguagesSection,
  VolunteeringSection,
  HobbiesSection,
} from "./additional-sections";
import { useResumeCustomization } from "@/contexts/ResumeCustomizationContext";

/**
 * Modern Two-Column Resume Template
 * - Two column layout with sidebar
 * - Modern typography
 * - ATS-friendly structure
 * - Clean design with subtle colors
 * - Professional appearance
 */
export const ModernTemplate: React.FC<TemplateProps> = ({
  resume,
  className = "",
}) => {
  const { getColorScheme, getFontFamily, getSpacing, getMargins } =
    useResumeCustomization();
  const colorScheme = getColorScheme();
  const fontFamily = getFontFamily();
  const spacing = getSpacing();
  const margins = getMargins();
  // Main content (left column)
  const mainContent = (
    <>
      <ProfessionalSummary bio={resume.bio?.body} />
      <ExperienceSection experiences={resume.experiences} />
      <EducationSection educations={resume.educations} />
      <ProjectsSection projects={resume.projects} />
      <VolunteeringSection volunteering={resume.volunteerings} />
    </>
  );

  // Sidebar content (right column)
  const sidebarContent = (
    <>
      <SkillsSection skills={resume.skills} />
      <CertificationsSection certifications={resume.certifications} />
      <AwardsSection awards={resume.awards} />
      <LanguagesSection languages={resume.languages} />
      <HobbiesSection hobbies={resume.hobbies} />
    </>
  );

  return (
    <div
      className={`modern-template bg-white text-gray-900 ${className}`}
      style={
        {
          fontFamily: fontFamily.family,
          lineHeight: spacing.value * 1.5,
          "--primary-color": colorScheme.primary,
          "--secondary-color": colorScheme.secondary,
          "--text-color": colorScheme.text,
          "--accent-color": colorScheme.accent,
          "--spacing-multiplier": spacing.value,
        } as React.CSSProperties
      }
    >
      <div
        className="max-w-6xl mx-auto font-sans"
        style={{
          padding: `${margins.value * 32}px`,
        }}
      >
        {/* Header Section */}
        <ResumeHeader headerStyle="split" resume={resume} showPhoto={true} />

        {/* Two Column Layout */}
        <TwoColumnLayout
          leftColumn={mainContent}
          rightColumn={
            <div className="sidebar bg-gray-50 p-6 rounded-lg">
              {sidebarContent}
            </div>
          }
        />
      </div>

      <style jsx>{`
        .modern-template h1 {
          font-size: 32px;
          font-weight: 700;
          margin-bottom: calc(var(--spacing-multiplier) * 8px);
          color: var(--text-color);
          letter-spacing: -0.025em;
        }

        .modern-template h2 {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: calc(var(--spacing-multiplier) * 16px);
          color: var(--text-color);
          text-transform: uppercase;
          letter-spacing: 0.05em;
          border-bottom: 2px solid var(--primary-color);
          padding-bottom: 6px;
        }

        .modern-template .sidebar h2 {
          border-bottom: 2px solid var(--accent-color);
          font-size: 16px;
          margin-bottom: calc(var(--spacing-multiplier) * 12px);
        }

        .modern-template h3 {
          font-size: 15px;
          font-weight: 600;
          margin-bottom: calc(var(--spacing-multiplier) * 4px);
          color: var(--text-color);
        }

        .modern-template .contact-info {
          font-size: 14px;
          color: var(--secondary-color);
          font-weight: 400;
        }

        .modern-template .section-content {
          font-size: 14px;
          color: var(--text-color);
        }

        .modern-template .sidebar .section-content {
          font-size: 13px;
        }

        .modern-template .experience-item,
        .modern-template .education-item,
        .modern-template .project-item,
        .modern-template .volunteering-item {
          margin-bottom: calc(var(--spacing-multiplier) * 20px);
          page-break-inside: avoid;
        }

        .modern-template .sidebar .certification-item,
        .modern-template .sidebar .award-item {
          margin-bottom: calc(var(--spacing-multiplier) * 16px);
          padding-bottom: calc(var(--spacing-multiplier) * 12px);
          border-bottom: 1px solid var(--accent-color);
        }

        .modern-template .sidebar .certification-item:last-child,
        .modern-template .sidebar .award-item:last-child {
          border-bottom: none;
          padding-bottom: 0;
        }

        .modern-template .skill-category {
          margin-bottom: calc(var(--spacing-multiplier) * 12px);
          padding: calc(var(--spacing-multiplier) * 8px) 0;
        }

        .modern-template .skill-category span:first-child {
          color: var(--text-color);
          font-weight: 500;
        }

        .modern-template .language-item {
          margin-bottom: calc(var(--spacing-multiplier) * 6px);
          padding: calc(var(--spacing-multiplier) * 4px) 0;
        }

        .modern-template .sidebar {
          background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
          border: 1px solid #e5e7eb;
        }

        /* Print optimizations */
        @media print {
          .modern-template {
            font-size: 12px;
            line-height: 1.4;
          }

          .modern-template h1 {
            font-size: 26px;
          }

          .modern-template h2 {
            font-size: 16px;
            margin-top: 16px;
            margin-bottom: 10px;
          }

          .modern-template .sidebar {
            background: #f8f9fa !important;
            border: 1px solid #dee2e6 !important;
          }

          .modern-template .resume-section {
            margin-bottom: 14px;
          }
        }

        /* ATS Optimization */
        .modern-template * {
          box-sizing: border-box;
        }

        .modern-template .sidebar {
          /* Ensure sidebar content is still readable by ATS */
          background-color: transparent !important;
        }

        @media screen and (max-width: 1024px) {
          .modern-template .sidebar {
            background: transparent;
            padding: 0;
            border: none;
          }
        }
      `}</style>
    </div>
  );
};

export default ModernTemplate;
