import React from "react";
import {
  TemplateProps,
  ResumeHeader,
  ProfessionalSummary,
  ExperienceSection,
  EducationSection,
  SkillsSection,
} from "./base-components";
import { SingleColumnLayout } from "./additional-sections";
import {
  ProjectsSection,
  CertificationsSection,
  AwardsSection,
  LanguagesSection,
  VolunteeringSection,
  HobbiesSection,
} from "./additional-sections";

/**
 * Minimalist Resume Template
 * - Clean, minimal design
 * - Single column layout
 * - Maximum ATS compatibility
 * - Focus on content over design
 * - Excellent readability
 */
export const MinimalistTemplate: React.FC<TemplateProps> = ({
  resume,
  className = "",
}) => {
  return (
    <div className={`minimalist-template bg-white text-black ${className}`}>
      <div className="max-w-4xl mx-auto p-8 font-mono">
        {/* Header Section */}
        <ResumeHeader headerStyle="left" resume={resume} showPhoto={false} />

        <SingleColumnLayout>
          {/* Professional Summary */}
          <ProfessionalSummary bio={resume.bio?.body} />

          {/* Professional Experience */}
          <ExperienceSection experiences={resume.experiences} />

          {/* Education */}
          <EducationSection educations={resume.educations} />

          {/* Skills */}
          <SkillsSection skills={resume.skills} />

          {/* Projects */}
          <ProjectsSection projects={resume.projects} />

          {/* Certifications */}
          <CertificationsSection certifications={resume.certifications} />

          {/* Awards */}
          <AwardsSection awards={resume.awards} />

          {/* Languages */}
          <LanguagesSection languages={resume.languages} />

          {/* Volunteer Experience */}
          <VolunteeringSection volunteering={resume.volunteerings} />

          {/* Interests & Hobbies */}
          <HobbiesSection hobbies={resume.hobbies} />
        </SingleColumnLayout>
      </div>

      <style jsx>{`
        .minimalist-template {
          font-family: "Courier New", "Monaco", "Menlo", monospace;
          line-height: 1.6;
          color: #000000;
        }

        .minimalist-template h1 {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 4px;
          color: #000000;
          text-transform: uppercase;
          letter-spacing: 1px;
        }

        .minimalist-template h2 {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 16px;
          margin-top: 24px;
          color: #000000;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          border-bottom: 1px solid #000000;
          padding-bottom: 2px;
        }

        .minimalist-template h3 {
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 2px;
          color: #000000;
        }

        .minimalist-template .contact-info {
          font-size: 12px;
          color: #000000;
          margin-bottom: 8px;
        }

        .minimalist-template .section-content {
          font-size: 12px;
          color: #000000;
        }

        .minimalist-template .experience-item,
        .minimalist-template .education-item,
        .minimalist-template .project-item,
        .minimalist-template .certification-item,
        .minimalist-template .award-item,
        .minimalist-template .volunteering-item {
          margin-bottom: 16px;
          page-break-inside: avoid;
        }

        .minimalist-template .skill-category {
          margin-bottom: 8px;
        }

        .minimalist-template .skill-category span:first-child {
          font-weight: bold;
        }

        .minimalist-template .language-item {
          margin-bottom: 4px;
        }

        .minimalist-template .language-item span:first-child {
          font-weight: bold;
        }

        /* Remove all colors and decorations for maximum ATS compatibility */
        .minimalist-template * {
          background: transparent !important;
          border-color: #000000 !important;
          color: #000000 !important;
        }

        .minimalist-template a {
          text-decoration: underline;
        }

        /* Print optimizations */
        @media print {
          .minimalist-template {
            font-size: 11px;
            line-height: 1.4;
          }

          .minimalist-template h1 {
            font-size: 20px;
          }

          .minimalist-template h2 {
            font-size: 14px;
            margin-top: 18px;
            margin-bottom: 10px;
          }

          .minimalist-template .resume-section {
            margin-bottom: 14px;
          }

          .minimalist-template .experience-item,
          .minimalist-template .education-item,
          .minimalist-template .project-item {
            margin-bottom: 12px;
          }
        }

        /* Maximum ATS Optimization */
        .minimalist-template {
          /* Use standard fonts that ATS can read */
          font-family: Arial, sans-serif;
        }

        .minimalist-template * {
          box-sizing: border-box;
          /* Remove any styling that might confuse ATS */
          text-shadow: none !important;
          box-shadow: none !important;
          border-radius: 0 !important;
        }

        .minimalist-template table {
          border-collapse: collapse;
          width: 100%;
        }

        .minimalist-template img {
          max-width: 100%;
          height: auto;
        }

        /* Ensure proper text hierarchy for ATS */
        .minimalist-template h1,
        .minimalist-template h2,
        .minimalist-template h3,
        .minimalist-template h4,
        .minimalist-template h5,
        .minimalist-template h6 {
          font-weight: bold;
          margin-top: 0;
        }

        .minimalist-template p {
          margin: 0 0 8px 0;
        }

        .minimalist-template ul,
        .minimalist-template ol {
          margin: 0 0 8px 20px;
          padding: 0;
        }

        .minimalist-template li {
          margin-bottom: 4px;
        }
      `}</style>
    </div>
  );
};

export default MinimalistTemplate;
