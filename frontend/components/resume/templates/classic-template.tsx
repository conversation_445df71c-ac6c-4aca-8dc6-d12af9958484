import React from "react";
import {
  TemplateProps,
  ResumeHeader,
  ProfessionalSummary,
  ExperienceSection,
  EducationSection,
  SkillsSection,
} from "./base-components";
import { SingleColumnLayout } from "./additional-sections";
import {
  ProjectsSection,
  CertificationsSection,
  AwardsSection,
  LanguagesSection,
  VolunteeringSection,
  HobbiesSection,
} from "./additional-sections";
import { useResumeCustomization } from "@/contexts/ResumeCustomizationContext";

/**
 * Classic Professional Resume Template
 * - Single column layout
 * - Traditional formatting
 * - ATS-friendly structure
 * - Clean typography
 * - Professional appearance
 */
export const ClassicTemplate: React.FC<TemplateProps> = ({
  resume,
  className = "",
}) => {
  const { getColorScheme, getFontFamily, getSpacing, getMargins } =
    useResumeCustomization();
  const colorScheme = getColorScheme();
  const fontFamily = getFontFamily();
  const spacing = getSpacing();
  const margins = getMargins();

  console.log({colorScheme, fontFamily, spacing, margins});

  return (
    <div
      className={`classic-template bg-white text-gray-900 ${className}`}
      style={{
        fontFamily: fontFamily.family,
        lineHeight: spacing.value * 1.4,
        padding: `${margins.value * 32}px`,
      }}
    >
      <div className="max-w-4xl mx-auto font-sans leading-normal">
        {/* Header Section */}
        <ResumeHeader
          headerStyle="centered"
          resume={resume}
          showPhoto={false}
        />

        <SingleColumnLayout>
          {/* Professional Summary */}
          <ProfessionalSummary bio={resume.bio?.body} />

          {/* Professional Experience */}
          <ExperienceSection experiences={resume.experiences} />

          {/* Education */}
          <EducationSection educations={resume.educations} />

          {/* Skills */}
          <SkillsSection skills={resume.skills} />

          {/* Projects */}
          <ProjectsSection projects={resume.projects} />

          {/* Certifications */}
          <CertificationsSection certifications={resume.certifications} />

          {/* Awards */}
          <AwardsSection awards={resume.awards} />

          {/* Languages */}
          <LanguagesSection languages={resume.languages} />

          {/* Volunteer Experience */}
          <VolunteeringSection volunteering={resume.volunteerings} />

          {/* Interests & Hobbies */}
          <HobbiesSection hobbies={resume.hobbies} />
        </SingleColumnLayout>
      </div>

      <style jsx>{`
        .classic-template h1 {
          font-size: 28px;
          font-weight: bold;
          margin-bottom: ${spacing.value * 8}px;
          color: ${colorScheme.text};
        }

        .classic-template h2 {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: ${spacing.value * 12}px;
          color: ${colorScheme.primary};
          text-transform: uppercase;
          letter-spacing: 0.5px;
          border-bottom: 2px solid ${colorScheme.primary};
          padding-bottom: 4px;
        }

        .classic-template h3 {
          font-size: 14px;
          font-weight: 600;
          margin-bottom: ${spacing.value * 4}px;
          color: ${colorScheme.text};
        }

        .classic-template .contact-info {
          font-size: 12px;
          color: ${colorScheme.secondary};
        }

        .classic-template .section-content {
          font-size: 12px;
          color: ${colorScheme.text};
        }

        .classic-template .experience-item,
        .classic-template .education-item,
        .classic-template .project-item,
        .classic-template .certification-item,
        .classic-template .award-item,
        .classic-template .volunteering-item {
          margin-bottom: ${spacing.value * 16}px;
          page-break-inside: avoid;
        }

        .classic-template .skill-category {
          margin-bottom: ${spacing.value * 8}px;
        }

        .classic-template .language-item {
          margin-bottom: ${spacing.value * 4}px;
        }

        /* Print optimizations */
        @media print {
          .classic-template {
            font-size: 11px;
            line-height: 1.3;
          }

          .classic-template h1 {
            font-size: 24px;
          }

          .classic-template h2 {
            font-size: 16px;
            margin-top: 16px;
            margin-bottom: 8px;
          }

          .classic-template .resume-section {
            margin-bottom: 16px;
          }

          .classic-template .experience-item,
          .classic-template .education-item,
          .classic-template .project-item {
            margin-bottom: 12px;
          }
        }

        /* ATS Optimization */
        .classic-template * {
          box-sizing: border-box;
        }

        .classic-template table {
          border-collapse: collapse;
          width: 100%;
        }

        .classic-template img {
          max-width: 100%;
          height: auto;
        }
      `}</style>
    </div>
  );
};

export default ClassicTemplate;
