import React from "react";
import {
  TemplateProps,
  ResumeHeader,
  ProfessionalSummary,
  ExperienceSection,
  EducationSection,
  SkillsSection,
} from "./base-components";
import { SingleColumnLayout } from "./additional-sections";
import {
  ProjectsSection,
  CertificationsSection,
  AwardsSection,
  LanguagesSection,
  VolunteeringSection,
  HobbiesSection,
} from "./additional-sections";
import { useResumeCustomization } from "@/contexts/ResumeCustomizationContext";

/**
 * Classic Professional Resume Template
 * - Single column layout
 * - Traditional formatting
 * - ATS-friendly structure
 * - Clean typography
 * - Professional appearance
 */
// Helper component for section headings
const SectionHeading: React.FC<{ children: React.ReactNode; colorScheme: any; spacing: any }> = ({
  children,
  colorScheme,
  spacing
}) => (
  <h2
    style={{
      fontSize: "18px",
      fontWeight: 600,
      marginBottom: `${spacing.value * 12}px`,
      color: colorScheme.primary,
      textTransform: "uppercase" as const,
      letterSpacing: "0.5px",
      borderBottom: `2px solid ${colorScheme.primary}`,
      paddingBottom: "4px",
    }}
  >
    {children}
  </h2>
);

export const ClassicTemplate: React.FC<TemplateProps> = ({
  resume,
  className = "",
}) => {
  const { getColorScheme, getFontFamily, getSpacing, getMargins } =
    useResumeCustomization();
  const colorScheme = getColorScheme();
  const fontFamily = getFontFamily();
  const spacing = getSpacing();
  const margins = getMargins();

  return (
    <div
      className={`classic-template bg-white text-gray-900 ${className}`}
      style={
        {
          fontFamily: fontFamily.family,
          lineHeight: spacing.value * 1.4,
          padding: `${margins.value * 32}px`,
          "--primary-color": colorScheme.primary,
          "--secondary-color": colorScheme.secondary,
          "--text-color": colorScheme.text,
          "--accent-color": colorScheme.accent,
          "--spacing-multiplier": spacing.value,
        } as React.CSSProperties
      }
    >
      <div className="max-w-4xl mx-auto font-sans leading-normal">
        {/* Header Section */}
        <ResumeHeader
          headerStyle="centered"
          resume={resume}
          showPhoto={false}
        />

        <SingleColumnLayout>
          {/* Professional Summary */}
          <div style={{ marginBottom: `${spacing.value * 16}px` }}>
            <SectionHeading colorScheme={colorScheme} spacing={spacing}>
              Professional Summary
            </SectionHeading>
            <ProfessionalSummary bio={resume.bio?.body} />
          </div>

          {/* Professional Experience */}
          <div style={{ marginBottom: `${spacing.value * 16}px` }}>
            <SectionHeading colorScheme={colorScheme} spacing={spacing}>
              Professional Experience
            </SectionHeading>
            <ExperienceSection experiences={resume.experiences} />
          </div>

          {/* Education */}
          <div style={{ marginBottom: `${spacing.value * 16}px` }}>
            <SectionHeading colorScheme={colorScheme} spacing={spacing}>
              Education
            </SectionHeading>
            <EducationSection educations={resume.educations} />
          </div>

          {/* Skills */}
          <div style={{ marginBottom: `${spacing.value * 16}px` }}>
            <SectionHeading colorScheme={colorScheme} spacing={spacing}>
              Skills
            </SectionHeading>
            <SkillsSection skills={resume.skills} />
          </div>

          {/* Projects */}
          {resume.projects && resume.projects.length > 0 && (
            <div style={{ marginBottom: `${spacing.value * 16}px` }}>
              <h2 style={{
                color: colorScheme.primary,
                borderBottomColor: colorScheme.primary,
                marginBottom: `${spacing.value * 12}px`
              }} className="section-heading">
                Projects
              </h2>
              <ProjectsSection projects={resume.projects} />
            </div>
          )}

          {/* Certifications */}
          {resume.certifications && resume.certifications.length > 0 && (
            <div style={{ marginBottom: `${spacing.value * 16}px` }}>
              <h2 style={{
                color: colorScheme.primary,
                borderBottomColor: colorScheme.primary,
                marginBottom: `${spacing.value * 12}px`
              }} className="section-heading">
                Certifications
              </h2>
              <CertificationsSection certifications={resume.certifications} />
            </div>
          )}

          {/* Awards */}
          {resume.awards && resume.awards.length > 0 && (
            <div style={{ marginBottom: `${spacing.value * 16}px` }}>
              <h2 style={{
                color: colorScheme.primary,
                borderBottomColor: colorScheme.primary,
                marginBottom: `${spacing.value * 12}px`
              }} className="section-heading">
                Awards
              </h2>
              <AwardsSection awards={resume.awards} />
            </div>
          )}

          {/* Languages */}
          {resume.languages && resume.languages.length > 0 && (
            <div style={{ marginBottom: `${spacing.value * 16}px` }}>
              <h2 style={{
                color: colorScheme.primary,
                borderBottomColor: colorScheme.primary,
                marginBottom: `${spacing.value * 12}px`
              }} className="section-heading">
                Languages
              </h2>
              <LanguagesSection languages={resume.languages} />
            </div>
          )}

          {/* Volunteer Experience */}
          {resume.volunteerings && resume.volunteerings.length > 0 && (
            <div style={{ marginBottom: `${spacing.value * 16}px` }}>
              <h2 style={{
                color: colorScheme.primary,
                borderBottomColor: colorScheme.primary,
                marginBottom: `${spacing.value * 12}px`
              }} className="section-heading">
                Volunteer Experience
              </h2>
              <VolunteeringSection volunteering={resume.volunteerings} />
            </div>
          )}

          {/* Interests & Hobbies */}
          {resume.hobbies && resume.hobbies.length > 0 && (
            <div style={{ marginBottom: `${spacing.value * 16}px` }}>
              <h2 style={{
                color: colorScheme.primary,
                borderBottomColor: colorScheme.primary,
                marginBottom: `${spacing.value * 12}px`
              }} className="section-heading">
                Interests & Hobbies
              </h2>
              <HobbiesSection hobbies={resume.hobbies} />
            </div>
          )}
        </SingleColumnLayout>
      </div>

      <style jsx>{`
        .classic-template h1 {
          font-size: 28px;
          font-weight: bold;
          margin-bottom: calc(var(--spacing-multiplier) * 8px);
          color: var(--text-color);
        }

        .classic-template h2 {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: calc(var(--spacing-multiplier) * 12px);
          color: var(--primary-color);
          text-transform: uppercase;
          letter-spacing: 0.5px;
          border-bottom: 2px solid var(--primary-color);
          padding-bottom: 4px;
        }

        .classic-template h3 {
          font-size: 14px;
          font-weight: 600;
          margin-bottom: calc(var(--spacing-multiplier) * 4px);
          color: var(--text-color);
        }

        .classic-template .contact-info {
          font-size: 12px;
          color: var(--secondary-color);
        }

        .classic-template .section-content {
          font-size: 12px;
          color: var(--text-color);
        }

        .classic-template .experience-item,
        .classic-template .education-item,
        .classic-template .project-item,
        .classic-template .certification-item,
        .classic-template .award-item,
        .classic-template .volunteering-item {
          margin-bottom: calc(var(--spacing-multiplier) * 16px);
          page-break-inside: avoid;
        }

        .classic-template .skill-category {
          margin-bottom: calc(var(--spacing-multiplier) * 8px);
        }

        .classic-template .language-item {
          margin-bottom: calc(var(--spacing-multiplier) * 4px);
        }

        /* Print optimizations */
        @media print {
          .classic-template {
            font-size: 11px;
            line-height: 1.3;
          }

          .classic-template h1 {
            font-size: 24px;
          }

          .classic-template h2 {
            font-size: 16px;
            margin-top: 16px;
            margin-bottom: 8px;
          }

          .classic-template .resume-section {
            margin-bottom: 16px;
          }

          .classic-template .experience-item,
          .classic-template .education-item,
          .classic-template .project-item {
            margin-bottom: 12px;
          }
        }

        /* ATS Optimization */
        .classic-template * {
          box-sizing: border-box;
        }

        .classic-template table {
          border-collapse: collapse;
          width: 100%;
        }

        .classic-template img {
          max-width: 100%;
          height: auto;
        }
      `}</style>
    </div>
  );
};

export default ClassicTemplate;
