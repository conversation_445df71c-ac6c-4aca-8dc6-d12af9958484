import React from "react";
import {
  TemplateProps,
  ResumeHeader,
  ProfessionalSummary,
  ExperienceSection,
  EducationSection,
  SkillsSection,
} from "./base-components";
import { SingleColumnLayout } from "./additional-sections";
import {
  ProjectsSection,
  CertificationsSection,
  AwardsSection,
  LanguagesSection,
  VolunteeringSection,
  HobbiesSection,
} from "./additional-sections";

/**
 * Classic Professional Resume Template
 * - Single column layout
 * - Traditional formatting
 * - ATS-friendly structure
 * - Clean typography
 * - Professional appearance
 */
export const ClassicTemplate: React.FC<TemplateProps> = ({
  resume,
  className = "",
}) => {
  return (
    <div className={`classic-template bg-white text-gray-900 ${className}`}>
      <div className="max-w-4xl mx-auto p-8 font-sans leading-normal">
        {/* Header Section */}
        <ResumeHeader
          headerStyle="centered"
          resume={resume}
          showPhoto={false}
        />

        <SingleColumnLayout>
          {/* Professional Summary */}
          <ProfessionalSummary bio={resume.bio?.body} />

          {/* Professional Experience */}
          <ExperienceSection experiences={resume.experiences} />

          {/* Education */}
          <EducationSection educations={resume.educations} />

          {/* Skills */}
          <SkillsSection skills={resume.skills} />

          {/* Projects */}
          <ProjectsSection projects={resume.projects} />

          {/* Certifications */}
          <CertificationsSection certifications={resume.certifications} />

          {/* Awards */}
          <AwardsSection awards={resume.awards} />

          {/* Languages */}
          <LanguagesSection languages={resume.languages} />

          {/* Volunteer Experience */}
          <VolunteeringSection volunteering={resume.volunteerings} />

          {/* Interests & Hobbies */}
          <HobbiesSection hobbies={resume.hobbies} />
        </SingleColumnLayout>
      </div>

      <style jsx>{`
        .classic-template {
          font-family: "Times New Roman", serif;
          line-height: 1.4;
        }

        .classic-template h1 {
          font-size: 28px;
          font-weight: bold;
          margin-bottom: 8px;
          color: #1a1a1a;
        }

        .classic-template h2 {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 12px;
          color: #2d2d2d;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          border-bottom: 1px solid #ccc;
          padding-bottom: 4px;
        }

        .classic-template h3 {
          font-size: 14px;
          font-weight: 600;
          margin-bottom: 4px;
          color: #1a1a1a;
        }

        .classic-template .contact-info {
          font-size: 12px;
          color: #555;
        }

        .classic-template .section-content {
          font-size: 12px;
          color: #333;
        }

        .classic-template .experience-item,
        .classic-template .education-item,
        .classic-template .project-item,
        .classic-template .certification-item,
        .classic-template .award-item,
        .classic-template .volunteering-item {
          margin-bottom: 16px;
          page-break-inside: avoid;
        }

        .classic-template .skill-category {
          margin-bottom: 8px;
        }

        .classic-template .language-item {
          margin-bottom: 4px;
        }

        /* Print optimizations */
        @media print {
          .classic-template {
            font-size: 11px;
            line-height: 1.3;
          }

          .classic-template h1 {
            font-size: 24px;
          }

          .classic-template h2 {
            font-size: 16px;
            margin-top: 16px;
            margin-bottom: 8px;
          }

          .classic-template .resume-section {
            margin-bottom: 16px;
          }

          .classic-template .experience-item,
          .classic-template .education-item,
          .classic-template .project-item {
            margin-bottom: 12px;
          }
        }

        /* ATS Optimization */
        .classic-template * {
          box-sizing: border-box;
        }

        .classic-template table {
          border-collapse: collapse;
          width: 100%;
        }

        .classic-template img {
          max-width: 100%;
          height: auto;
        }
      `}</style>
    </div>
  );
};

export default ClassicTemplate;
