import React from "react";
import {
  Template<PERSON>rops,
  ResumeHeader,
  ProfessionalSummary,
  ExperienceSection,
  EducationSection,
  SkillsSection,
} from "./base-components";
import { TwoColumnLayout } from "./additional-sections";
import {
  ProjectsSection,
  CertificationsSection,
  AwardsSection,
  LanguagesSection,
  VolunteeringSection,
  HobbiesSection,
} from "./additional-sections";

/**
 * Creative Resume Template
 * - Creative design with professional structure
 * - Two column layout with accent colors
 * - ATS-friendly with visual appeal
 * - Suitable for creative industries
 * - Maintains readability and professionalism
 */
export const CreativeTemplate: React.FC<TemplateProps> = ({
  resume,
  className = "",
}) => {
  // Main content (left column)
  const mainContent = (
    <>
      <ProfessionalSummary bio={resume.bio?.body} />
      <ExperienceSection experiences={resume.experiences} />
      <ProjectsSection projects={resume.projects} />
      <EducationSection educations={resume.educations} />
      <VolunteeringSection volunteering={resume.volunteerings} />
    </>
  );

  // Sidebar content (right column)
  const sidebarContent = (
    <>
      <SkillsSection skills={resume.skills} />
      <CertificationsSection certifications={resume.certifications} />
      <AwardsSection awards={resume.awards} />
      <LanguagesSection languages={resume.languages} />
      <HobbiesSection hobbies={resume.hobbies} />
    </>
  );

  return (
    <div className={`creative-template bg-white text-gray-900 ${className}`}>
      <div className="max-w-6xl mx-auto font-sans">
        {/* Header Section with Accent */}
        <div className="header-accent bg-gradient-to-r from-blue-600 to-purple-600 p-8 text-white">
          <ResumeHeader headerStyle="split" resume={resume} showPhoto={true} />
        </div>

        <div className="p-8">
          {/* Two Column Layout */}
          <TwoColumnLayout
            leftColumn={mainContent}
            rightColumn={<div className="sidebar">{sidebarContent}</div>}
          />
        </div>
      </div>

      <style jsx>{`
        .creative-template {
          font-family:
            "Poppins", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
        }

        .creative-template .header-accent h1 {
          font-size: 36px;
          font-weight: 700;
          margin-bottom: 8px;
          color: white;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .creative-template .header-accent h2 {
          font-size: 20px;
          font-weight: 400;
          margin-bottom: 16px;
          color: rgba(255, 255, 255, 0.9);
        }

        .creative-template .header-accent .contact-info {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
        }

        .creative-template h2 {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 16px;
          color: #2563eb;
          text-transform: uppercase;
          letter-spacing: 0.05em;
          position: relative;
          padding-left: 20px;
        }

        .creative-template h2::before {
          content: "";
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 12px;
          height: 12px;
          background: linear-gradient(135deg, #2563eb, #7c3aed);
          border-radius: 50%;
        }

        .creative-template .sidebar h2 {
          color: #7c3aed;
          border-left: 4px solid #7c3aed;
          padding-left: 16px;
          margin-left: 0;
        }

        .creative-template .sidebar h2::before {
          display: none;
        }

        .creative-template h3 {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 4px;
          color: #1f2937;
        }

        .creative-template .section-content {
          font-size: 14px;
          color: #374151;
        }

        .creative-template .experience-item,
        .creative-template .education-item,
        .creative-template .project-item,
        .creative-template .volunteering-item {
          margin-bottom: 24px;
          padding: 16px;
          border-left: 3px solid #e5e7eb;
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          border-radius: 0 8px 8px 0;
          page-break-inside: avoid;
        }

        .creative-template .experience-item:hover,
        .creative-template .education-item:hover,
        .creative-template .project-item:hover,
        .creative-template .volunteering-item:hover {
          border-left-color: #2563eb;
          transform: translateX(2px);
          transition: all 0.2s ease;
        }

        .creative-template .sidebar .certification-item,
        .creative-template .sidebar .award-item {
          margin-bottom: 16px;
          padding: 12px;
          background: linear-gradient(135deg, #fef7ff 0%, #f3e8ff 100%);
          border-radius: 8px;
          border: 1px solid #e9d5ff;
        }

        .creative-template .skill-category {
          margin-bottom: 12px;
          padding: 8px 12px;
          background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
          border-radius: 6px;
          border-left: 3px solid #2563eb;
        }

        .creative-template .skill-category span:first-child {
          color: #1e40af;
          font-weight: 600;
        }

        .creative-template .language-item {
          margin-bottom: 8px;
          padding: 6px 12px;
          background: #f8fafc;
          border-radius: 4px;
          border: 1px solid #e2e8f0;
        }

        .creative-template .language-item span:first-child {
          color: #475569;
          font-weight: 500;
        }

        /* Print optimizations - remove colors and effects for ATS */
        @media print {
          .creative-template {
            font-size: 12px;
            line-height: 1.4;
          }

          .creative-template .header-accent {
            background: white !important;
            color: black !important;
            border-bottom: 2px solid black;
          }

          .creative-template .header-accent h1,
          .creative-template .header-accent h2,
          .creative-template .header-accent .contact-info {
            color: black !important;
            text-shadow: none !important;
          }

          .creative-template h2 {
            color: black !important;
            font-size: 16px;
            margin-top: 16px;
            margin-bottom: 10px;
          }

          .creative-template h2::before {
            background: black !important;
          }

          .creative-template .sidebar h2 {
            border-left-color: black !important;
          }

          .creative-template .experience-item,
          .creative-template .education-item,
          .creative-template .project-item,
          .creative-template .volunteering-item,
          .creative-template .certification-item,
          .creative-template .award-item,
          .creative-template .skill-category,
          .creative-template .language-item {
            background: white !important;
            border-color: black !important;
            box-shadow: none !important;
            transform: none !important;
          }
        }

        /* ATS Optimization */
        .creative-template * {
          box-sizing: border-box;
        }

        /* Ensure content is accessible without colors */
        @media (prefers-reduced-motion: reduce) {
          .creative-template * {
            transition: none !important;
            transform: none !important;
          }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
          .creative-template .header-accent {
            background: black !important;
          }

          .creative-template h2 {
            color: black !important;
          }

          .creative-template .experience-item,
          .creative-template .education-item,
          .creative-template .project-item {
            background: white !important;
            border: 2px solid black !important;
          }
        }
      `}</style>
    </div>
  );
};

export default CreativeTemplate;
