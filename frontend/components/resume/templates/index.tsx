// Export all template components
export { default as ClassicTemplate } from "./classic-template";
export { default as ModernTemplate } from "./modern-template";
export { default as MinimalistTemplate } from "./minimalist-template";
export { default as CreativeTemplate } from "./creative-template";

// Export base components
export * from "./base-components";
export * from "./additional-sections";

// Export template registry and utilities
export * from "./template-registry";

// Export main preview component
export { default as ResumePreview } from "../resume-preview";

// Template configuration for easy access
export const TEMPLATE_CONFIG = {
  DEFAULT_TEMPLATE: "classic",
  AVAILABLE_TEMPLATES: ["classic", "modern", "minimalist", "creative"],
  ATS_RECOMMENDED: ["classic", "minimalist"], // Templates with highest ATS scores
  CREATIVE_RECOMMENDED: ["creative", "modern"], // Templates for creative industries
} as const;

// Utility functions
export const getRecommendedTemplates = (industry?: string) => {
  switch (industry?.toLowerCase()) {
    case "tech":
    case "technology":
    case "software":
      return ["modern", "minimalist"];
    case "creative":
    case "design":
    case "marketing":
      return ["creative", "modern"];
    case "finance":
    case "banking":
    case "legal":
    case "consulting":
      return ["classic", "minimalist"];
    default:
      return ["classic", "modern"];
  }
};

export const getTemplateForATSOptimization = () => {
  return TEMPLATE_CONFIG.ATS_RECOMMENDED;
};

export const getTemplateForCreativeIndustries = () => {
  return TEMPLATE_CONFIG.CREATIVE_RECOMMENDED;
};
