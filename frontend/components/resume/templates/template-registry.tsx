import React from "react";
import ClassicTemplate from "./classic-template";
import ModernTemplate from "./modern-template";
import MinimalistTemplate from "./minimalist-template";
import CreativeTemplate from "./creative-template";
import { Resume } from "@/types/resume";
import { ResumeCustomizationProvider } from "@/contexts/ResumeCustomizationContext";

// Template metadata interface
export interface TemplateMetadata {
  id: string;
  name: string;
  description: string;
  category: "professional" | "creative" | "minimal" | "modern";
  atsScore: number; // 1-10 scale for ATS compatibility
  features: string[];
  preview: string; // URL to preview image
  component: React.ComponentType<{ resume: Resume; className?: string }>;
}

// Template registry
export const RESUME_TEMPLATES: Record<string, TemplateMetadata> = {
  classic: {
    id: "classic",
    name: "Classic Professional",
    description:
      "Traditional single-column layout with clean typography. Perfect for conservative industries.",
    category: "professional",
    atsScore: 10,
    features: [
      "Single column layout",
      "Traditional formatting",
      "Maximum ATS compatibility",
      "Professional appearance",
      "Print optimized",
    ],
    preview: "/templates/classic-preview.png",
    component: ClassicTemplate,
  },
  modern: {
    id: "modern",
    name: "Modern Two-Column",
    description:
      "Contemporary design with sidebar layout. Great for tech and modern industries.",
    category: "modern",
    atsScore: 8,
    features: [
      "Two column layout",
      "Modern typography",
      "Sidebar for skills",
      "Photo support",
      "Clean design",
    ],
    preview: "/templates/modern-preview.png",
    component: ModernTemplate,
  },
  minimalist: {
    id: "minimalist",
    name: "Minimalist",
    description:
      "Ultra-clean design focused on content. Highest ATS compatibility.",
    category: "minimal",
    atsScore: 10,
    features: [
      "Minimal design",
      "Maximum readability",
      "Highest ATS score",
      "Content focused",
      "No distractions",
    ],
    preview: "/templates/minimalist-preview.png",
    component: MinimalistTemplate,
  },
  creative: {
    id: "creative",
    name: "Creative Professional",
    description:
      "Stylish design with color accents. Perfect for creative industries.",
    category: "creative",
    atsScore: 7,
    features: [
      "Creative design",
      "Color accents",
      "Two column layout",
      "Visual appeal",
      "Professional structure",
    ],
    preview: "/templates/creative-preview.png",
    component: CreativeTemplate,
  },
};

// Get template by ID
export const getTemplate = (templateId: string): TemplateMetadata | null => {
  return RESUME_TEMPLATES[templateId] || null;
};

// Get all templates
export const getAllTemplates = (): TemplateMetadata[] => {
  return Object.values(RESUME_TEMPLATES);
};

// Get templates by category
export const getTemplatesByCategory = (
  category: TemplateMetadata["category"],
): TemplateMetadata[] => {
  return getAllTemplates().filter((template) => template.category === category);
};

// Get templates by ATS score range
export const getTemplatesByATSScore = (
  minScore: number,
  maxScore: number = 10,
): TemplateMetadata[] => {
  return getAllTemplates().filter(
    (template) =>
      template.atsScore >= minScore && template.atsScore <= maxScore,
  );
};

// Template renderer component
export const TemplateRenderer: React.FC<{
  templateId: string;
  resume: Resume;
  className?: string;
}> = ({ templateId, resume, className }) => {
  const template = getTemplate(templateId);

  if (!template) {
    console.warn(
      `Template with ID "${templateId}" not found. Falling back to classic template.`,
    );
    const fallbackTemplate = getTemplate("classic");
    if (!fallbackTemplate) {
      return <div className="error">No templates available</div>;
    }
    const FallbackComponent = fallbackTemplate.component;
    return <FallbackComponent className={className} resume={resume} />;
  }

  const TemplateComponent = template.component;
  return <TemplateComponent className={className} resume={resume} />;
};

// Template selector component
export const TemplateSelector: React.FC<{
  selectedTemplateId: string;
  onTemplateSelect: (templateId: string) => void;
  showPreview?: boolean;
}> = ({ selectedTemplateId, onTemplateSelect, showPreview = true }) => {
  const templates = getAllTemplates();

  return (
    <div className="template-selector">
      <h3 className="text-lg font-semibold mb-4">Choose a Template</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {templates.map((template) => (
          <div
            key={template.id}
            className={`template-option cursor-pointer border-2 rounded-lg p-4 transition-all ${
              selectedTemplateId === template.id
                ? "border-blue-500 bg-blue-50"
                : "border-gray-200 hover:border-gray-300"
            }`}
            onClick={() => onTemplateSelect(template.id)}
          >
            {showPreview && (
              <div className="preview-image mb-3 bg-gray-100 rounded h-32 flex items-center justify-center">
                <span className="text-gray-500 text-sm">Preview</span>
              </div>
            )}
            <h4 className="font-semibold text-sm mb-1">{template.name}</h4>
            <p className="text-xs text-gray-600 mb-2">{template.description}</p>
            <div className="flex items-center justify-between">
              <span
                className={`text-xs px-2 py-1 rounded ${
                  template.category === "professional"
                    ? "bg-blue-100 text-blue-800"
                    : template.category === "creative"
                      ? "bg-purple-100 text-purple-800"
                      : template.category === "modern"
                        ? "bg-green-100 text-green-800"
                        : "bg-gray-100 text-gray-800"
                }`}
              >
                {template.category}
              </span>
              <span className="text-xs text-gray-500">
                ATS: {template.atsScore}/10
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// ATS compatibility indicator
export const ATSCompatibilityIndicator: React.FC<{
  score: number;
  showDetails?: boolean;
}> = ({ score, showDetails = false }) => {
  const getScoreColor = (score: number): string => {
    if (score >= 9) return "text-green-600 bg-green-100";
    if (score >= 7) return "text-yellow-600 bg-yellow-100";
    return "text-red-600 bg-red-100";
  };

  const getScoreLabel = (score: number): string => {
    if (score >= 9) return "Excellent";
    if (score >= 7) return "Good";
    return "Fair";
  };

  return (
    <div className="ats-indicator">
      <div
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(score)}`}
      >
        ATS Score: {score}/10 ({getScoreLabel(score)})
      </div>
      {showDetails && (
        <div className="mt-2 text-xs text-gray-600">
          <p>
            ATS (Applicant Tracking System) compatibility measures how well
            resume parsing systems can read your resume.
          </p>
          <ul className="mt-1 list-disc list-inside">
            <li>9-10: Excellent - Maximum compatibility</li>
            <li>7-8: Good - Minor formatting may be lost</li>
            <li>5-6: Fair - Some content may be missed</li>
            <li>Below 5: Poor - Significant parsing issues</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default TemplateRenderer;
