import React from "react";
import { ResumeSection, formatDate, formatDateRange } from "./base-components";
import {
  ProjectType,
  CertificationType,
  AwardType,
  VolunteeringType,
  ReferenceType,
  HobbyType,
  LanguageType,
} from "@/types/resume";

// Projects Component - ATS optimized
export const ProjectsSection: React.FC<{ projects: ProjectType[] }> = ({
  projects,
}) => {
  if (!projects?.length) return null;

  return (
    <ResumeSection title="Projects">
      <div className="space-y-4">
        {projects.map((project, index) => (
          <div key={project.id || index} className="project-item">
            <div className="flex justify-between items-start mb-1">
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 text-sm">
                  {project.title}
                </h3>
                {project.client && (
                  <p className="text-gray-700 text-sm">
                    Client: {project.client}
                  </p>
                )}
              </div>
              <div className="text-right text-sm text-gray-600 ml-4">
                <p>{formatDateRange(project.start_date, project.end_date)}</p>
              </div>
            </div>
            {project.description && (
              <p className="text-sm text-gray-700 leading-relaxed mt-2">
                {project.description}
              </p>
            )}
            {project.url && (
              <p className="text-sm text-blue-600 mt-1">{project.url}</p>
            )}
          </div>
        ))}
      </div>
    </ResumeSection>
  );
};

// Certifications Component - ATS optimized
export const CertificationsSection: React.FC<{
  certifications: CertificationType[];
}> = ({ certifications }) => {
  if (!certifications?.length) return null;

  return (
    <ResumeSection title="Certifications">
      <div className="space-y-3">
        {certifications.map((cert, index) => (
          <div key={cert.id || index} className="certification-item">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 text-sm">
                  {cert.title}
                </h3>
                <p className="text-gray-700 text-sm">{cert.issuer}</p>
              </div>
              <div className="text-right text-sm text-gray-600 ml-4">
                <p>Obtained: {formatDate(cert.date_obtained)}</p>
                {cert.expiration_date && (
                  <p className="text-xs">
                    Expires: {formatDate(cert.expiration_date)}
                  </p>
                )}
              </div>
            </div>
            {cert.description && (
              <p className="text-sm text-gray-700 mt-1 leading-relaxed">
                {cert.description}
              </p>
            )}
          </div>
        ))}
      </div>
    </ResumeSection>
  );
};

// Awards Component - ATS optimized
export const AwardsSection: React.FC<{ awards: AwardType[] }> = ({
  awards,
}) => {
  if (!awards?.length) return null;

  return (
    <ResumeSection title="Awards & Achievements">
      <div className="space-y-3">
        {awards.map((award, index) => (
          <div key={award.id || index} className="award-item">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 text-sm">
                  {award.title}
                </h3>
                <p className="text-gray-700 text-sm">{award.issuer}</p>
              </div>
              <div className="text-right text-sm text-gray-600 ml-4">
                <p>{formatDate(award.date_recieved)}</p>
              </div>
            </div>
            {award.description && (
              <p className="text-sm text-gray-700 mt-1 leading-relaxed">
                {award.description}
              </p>
            )}
          </div>
        ))}
      </div>
    </ResumeSection>
  );
};

// Languages Component - ATS optimized
export const LanguagesSection: React.FC<{ languages: LanguageType[] }> = ({
  languages,
}) => {
  if (!languages?.length) return null;

  const getProficiencyLevel = (level: number): string => {
    if (level >= 90) return "Native";
    if (level >= 80) return "Fluent";
    if (level >= 70) return "Advanced";
    if (level >= 60) return "Intermediate";
    if (level >= 40) return "Basic";
    return "Beginner";
  };

  return (
    <ResumeSection title="Languages">
      <div className="grid grid-cols-2 gap-2">
        {languages.map((lang, index) => (
          <div
            key={lang.id || index}
            className="language-item flex justify-between"
          >
            <span className="text-gray-900 text-sm font-medium">
              {lang.name}
            </span>
            <span className="text-gray-600 text-sm">
              {getProficiencyLevel(lang.proficiency)}
            </span>
          </div>
        ))}
      </div>
    </ResumeSection>
  );
};

// Volunteering Component - ATS optimized
export const VolunteeringSection: React.FC<{
  volunteering: VolunteeringType[];
}> = ({ volunteering }) => {
  if (!volunteering?.length) return null;

  return (
    <ResumeSection title="Volunteer Experience">
      <div className="space-y-4">
        {volunteering.map((vol, index) => (
          <div key={vol.id || index} className="volunteering-item">
            <div className="flex justify-between items-start mb-1">
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 text-sm">
                  {vol.role}
                </h3>
                <p className="text-gray-700 text-sm">{vol.organization}</p>
              </div>
              {(vol.start_date || vol.end_date) && (
                <div className="text-right text-sm text-gray-600 ml-4">
                  <p>
                    {formatDateRange(vol.start_date || "", vol.end_date || "")}
                  </p>
                </div>
              )}
            </div>
            {vol.description && (
              <p className="text-sm text-gray-700 leading-relaxed mt-2">
                {vol.description}
              </p>
            )}
          </div>
        ))}
      </div>
    </ResumeSection>
  );
};

// References Component - ATS optimized
export const ReferencesSection: React.FC<{ references: ReferenceType[] }> = ({
  references,
}) => {
  if (!references?.length) return null;

  return (
    <ResumeSection title="References">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {references.map((ref, index) => (
          <div
            key={ref.id || index}
            className="reference-item border border-gray-200 p-3 rounded"
          >
            <h3 className="font-semibold text-gray-900 text-sm">{ref.name}</h3>
            <p className="text-gray-700 text-sm">{ref.position}</p>
            <p className="text-gray-700 text-sm">{ref.company}</p>
            <div className="mt-2 text-sm text-gray-600">
              {ref.email && <p>Email: {ref.email}</p>}
              {ref.phone && <p>Phone: {ref.phone}</p>}
            </div>
            {ref.description && (
              <p className="text-sm text-gray-700 mt-2 leading-relaxed">
                {ref.description}
              </p>
            )}
          </div>
        ))}
      </div>
    </ResumeSection>
  );
};

// Hobbies Component - ATS optimized
export const HobbiesSection: React.FC<{ hobbies: HobbyType[] }> = ({
  hobbies,
}) => {
  if (!hobbies?.length) return null;

  return (
    <ResumeSection title="Interests & Hobbies">
      <div className="text-sm text-gray-700">
        {hobbies.map((hobby) => hobby.name).join(", ")}
      </div>
    </ResumeSection>
  );
};

// Two-column layout helper
export const TwoColumnLayout: React.FC<{
  leftColumn: React.ReactNode;
  rightColumn: React.ReactNode;
  leftWidth?: string;
  rightWidth?: string;
}> = ({ leftColumn, rightColumn, leftWidth = "2/3", rightWidth = "1/3" }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <div className={`lg:col-span-2`}>{leftColumn}</div>
      <div className={`lg:col-span-1`}>{rightColumn}</div>
    </div>
  );
};

// Single column layout helper
export const SingleColumnLayout: React.FC<{
  children: React.ReactNode;
  maxWidth?: string;
}> = ({ children, maxWidth = "max-w-4xl" }) => {
  return <div className={`${maxWidth} mx-auto`}>{children}</div>;
};
