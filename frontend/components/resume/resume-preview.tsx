"use client";

import React, { useState, useEffect } from "react";
import {
  Button,
  Select,
  SelectItem,
  Card,
  CardBody,
  Tooltip,
  useDisclosure,
  Modal,
  ModalBody,
  ModalContent,
  Spacer,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import {
  Template<PERSON><PERSON>er,
  TemplateSelector,
  ATSCompatibilityIndicator,
  getTemplate,
  getAllTemplates,
} from "./templates/template-registry";
import { TemplateGallery } from "@/components/templates/TemplateGallery";
import { Resume } from "@/types/resume";

interface ResumePreviewProps {
  resume: Resume;
  className?: string;
  showTemplateSelector?: boolean;
  showControls?: boolean;
}

export const ResumePreview: React.FC<ResumePreviewProps> = ({
  resume,
  className = "",
  showTemplateSelector = true,
  showControls = true,
}) => {
  const [scale, setScale] = useState<number>(1);
  const [showTemplateModal, setShowTemplateModal] = useState<boolean>(false);
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>(
    resume.template_id?.toString() || "classic",
  );

  // Template gallery modal state
  const {
    isOpen: isTemplateGalleryOpen,
    onOpen: onTemplateGalleryOpen,
    onOpenChange: onTemplateGalleryOpenChange,
  } = useDisclosure();

  // Update template when resume template_id changes
  useEffect(() => {
    if (resume.template_id) {
      const templateExists = getTemplate(resume.template_id.toString());
      if (templateExists) {
        setSelectedTemplateId(resume.template_id.toString());
      }
    }
  }, [resume.template_id, setSelectedTemplateId]);

  const currentTemplate = getTemplate(selectedTemplateId);
  const allTemplates = getAllTemplates();

  const handleTemplateChange = async (templateId: string) => {
    setShowTemplateModal(false);
    setSelectedTemplateId(templateId);
  };

  const handleZoomIn = () => {
    setScale((prev) => Math.min(prev + 0.1, 1.5));
  };

  const handleZoomOut = () => {
    setScale((prev) => Math.max(prev - 0.1, 0.3));
  };

  const handleResetZoom = () => {
    setScale(0.75);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownloadPDF = () => {
    // TODO: Implement PDF download functionality
    console.log("Download PDF functionality to be implemented");
  };

  return (
    <div className={`resume-preview flex flex-col h-full ${className}`}>
      {/* Controls */}
      {showControls && (
        <div className="preview-controls flex items-center justify-between p-4 border-b bg-gray-50">
          <div className="flex items-center gap-2">
            {/* Template Customization Button */}
            <Tooltip
              content={
                <div className="p-2">
                  <p className="font-semibold mb-1">Customize Your Resume</p>
                  <p className="text-sm text-gray-600">
                    Change templates, colors, fonts, and layout options
                  </p>
                </div>
              }
            >
              <Button
                className="font-medium"
                color="primary"
                size="sm"
                startContent={<Icon icon="lucide:palette" />}
                variant="flat"
                onPress={onTemplateGalleryOpen}
              >
                Customize
              </Button>
            </Tooltip>
          </div>

          <div className="flex items-center gap-2">
            {/* Zoom Controls */}
            <div className="flex items-center gap-1 border rounded-lg p-1">
              <Button
                isIconOnly
                isDisabled={scale <= 0.3}
                size="sm"
                variant="light"
                onPress={handleZoomOut}
              >
                <Icon icon="lucide:zoom-out" />
              </Button>
              <span className="text-sm px-2 min-w-[60px] text-center">
                {Math.round(scale * 100)}%
              </span>
              <Button
                isIconOnly
                isDisabled={scale >= 1.5}
                size="sm"
                variant="light"
                onPress={handleZoomIn}
              >
                <Icon icon="lucide:zoom-in" />
              </Button>
              <Button
                isIconOnly
                size="sm"
                variant="light"
                onPress={handleResetZoom}
              >
                <Icon icon="lucide:maximize" />
              </Button>
            </div>

            {/* Action Buttons */}
            <Button
              size="sm"
              startContent={<Icon icon="lucide:printer" />}
              variant="light"
              onPress={handlePrint}
            >
              Print
            </Button>
            <Button
              color="primary"
              size="sm"
              startContent={<Icon icon="lucide:download" />}
              onPress={handleDownloadPDF}
            >
              PDF
            </Button>
          </div>
        </div>
      )}

      {/* Template Selector Modal */}
      {showTemplateSelector && showTemplateModal && (
        <div className="template-modal-overlay fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <Card className="max-w-4xl w-full max-h-[80vh] overflow-auto">
            <CardBody>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Choose Template</h2>
                <Button
                  isIconOnly
                  variant="light"
                  onPress={() => setShowTemplateModal(false)}
                >
                  <Icon icon="lucide:x" />
                </Button>
              </div>
              <TemplateSelector
                selectedTemplateId={selectedTemplateId}
                showPreview={true}
                onTemplateSelect={handleTemplateChange}
              />
            </CardBody>
          </Card>
        </div>
      )}

      {/* Template Gallery Modal */}
      <Modal
        classNames={{
          base: "justify-start sm:m-0 p-0 h-dvh max-h-full w-[var(--sidebar-width)]",
          wrapper: "items-start justify-start !w-[var(--sidebar-width)]",
          body: "p-0",
          closeButton: "z-50",
        }}
        isOpen={isTemplateGalleryOpen}
        motionProps={{
          variants: {
            enter: {
              x: 0,
              transition: {
                duration: 0.3,
                ease: "easeOut",
              },
            },
            exit: {
              x: -288,
              transition: {
                duration: 0.2,
                ease: "easeOut",
              },
            },
          },
        }}
        radius="none"
        scrollBehavior="inside"
        style={{
          // @ts-ignore
          "--sidebar-width": "288px",
        }}
        onOpenChange={onTemplateGalleryOpenChange}
      >
        <ModalContent>
          <ModalBody>
            <div className="relative flex h-full w-72 flex-1 flex-col p-6">
              <div className="flex flex-col gap-3 px-2">
                <div className="flex items-center gap-2">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary">
                    <Icon className="text-white" icon="lucide:palette" />
                  </div>
                  <span className="text-lg font-bold text-foreground">
                    Customize Resume
                  </span>
                </div>
                <p className="text-sm text-gray-600 leading-relaxed">
                  Choose templates, colors, fonts, and styling options to make
                  your resume stand out.
                </p>
              </div>
              <Spacer y={6} />

              <div className="overflow-y-auto overflow-x-hidden h-full max-h-screen">
                {/* Customization Sections */}
                <div className="space-y-6">
                  {/* Templates Section */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-3">
                      <Icon
                        className="text-blue-600"
                        icon="lucide:layout-template"
                      />
                      <h3 className="font-semibold text-gray-900">Templates</h3>
                    </div>
                    <p className="text-xs text-gray-600 mb-4">
                      Choose from professional, modern, or creative designs
                    </p>
                    <TemplateGallery
                      selectedTemplate={selectedTemplateId}
                      setSelectedTemplate={(templateId) => {
                        setSelectedTemplateId(templateId);
                        onTemplateGalleryOpenChange();
                      }}
                      templates={allTemplates}
                    />
                  </div>

                  {/* Colors Section - Coming Soon */}
                  <div className="bg-gray-50 rounded-lg p-4 opacity-60">
                    <div className="flex items-center gap-2 mb-3">
                      <Icon className="text-purple-600" icon="lucide:palette" />
                      <h3 className="font-semibold text-gray-900">Colors</h3>
                      <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                        Coming Soon
                      </span>
                    </div>
                    <p className="text-xs text-gray-600 mb-3">
                      Customize accent colors and theme
                    </p>
                    <div className="flex gap-2">
                      {[
                        "bg-blue-500",
                        "bg-green-500",
                        "bg-purple-500",
                        "bg-red-500",
                        "bg-orange-500",
                      ].map((color, index) => (
                        <div
                          key={index}
                          className={`w-6 h-6 rounded-full ${color} cursor-not-allowed`}
                        />
                      ))}
                    </div>
                  </div>

                  {/* Fonts Section - Coming Soon */}
                  <div className="bg-gray-50 rounded-lg p-4 opacity-60">
                    <div className="flex items-center gap-2 mb-3">
                      <Icon className="text-green-600" icon="lucide:type" />
                      <h3 className="font-semibold text-gray-900">
                        Typography
                      </h3>
                      <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                        Coming Soon
                      </span>
                    </div>
                    <p className="text-xs text-gray-600">
                      Choose fonts and text styling options
                    </p>
                  </div>

                  {/* Layout Section - Coming Soon */}
                  <div className="bg-gray-50 rounded-lg p-4 opacity-60">
                    <div className="flex items-center gap-2 mb-3">
                      <Icon className="text-indigo-600" icon="lucide:layout" />
                      <h3 className="font-semibold text-gray-900">Layout</h3>
                      <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                        Coming Soon
                      </span>
                    </div>
                    <p className="text-xs text-gray-600">
                      Adjust spacing, margins, and section order
                    </p>
                  </div>
                </div>
              </div>

              <Spacer y={8} />
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* Preview Area */}
      <div className="preview-area flex-1 overflow-auto bg-gray-100 p-4">
        <div className="preview-container flex justify-center">
          <div
            className="resume-preview-content bg-white shadow-lg"
            style={{
              transform: `scale(${scale})`,
              transformOrigin: "top center",
              width: "8.5in",
              minHeight: "11in",
              margin: "0 auto",
            }}
          >
            <TemplateRenderer
              className="w-full h-full"
              resume={resume}
              templateId={selectedTemplateId}
            />
          </div>
        </div>
      </div>

      {/* ATS Score Display */}
      {currentTemplate && (
        <div className="ats-score-display p-3 border-t bg-gray-50">
          <ATSCompatibilityIndicator
            score={currentTemplate.atsScore}
            showDetails={false}
          />
        </div>
      )}

      <style jsx>{`
        .resume-preview {
          height: 100%;
        }

        .preview-area {
          background: #f5f5f5;
          background-image: radial-gradient(
            circle,
            #e0e0e0 1px,
            transparent 1px
          );
          background-size: 20px 20px;
        }

        .resume-preview-content {
          box-shadow:
            0 4px 6px -1px rgba(0, 0, 0, 0.1),
            0 2px 4px -1px rgba(0, 0, 0, 0.06);
          border-radius: 8px;
          overflow: hidden;
        }

        /* Print styles */
        @media print {
          .preview-controls,
          .ats-score-display {
            display: none !important;
          }

          .preview-area {
            background: white !important;
            padding: 0 !important;
          }

          .resume-preview-content {
            transform: none !important;
            box-shadow: none !important;
            border-radius: 0 !important;
            width: 100% !important;
            height: auto !important;
            margin: 0 !important;
          }

          .template-modal-overlay {
            display: none !important;
          }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
          .preview-controls {
            flex-direction: column;
            gap: 8px;
            align-items: stretch;
          }

          .preview-controls > div {
            justify-content: center;
          }
        }
      `}</style>
    </div>
  );
};

export default ResumePreview;
