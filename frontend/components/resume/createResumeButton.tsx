"use client";

import {
  But<PERSON>,
  Form,
  Input,
  Modal,
  Modal<PERSON>ody,
  Modal<PERSON>ontent,
  ModalHeader,
  useDisclosure,
} from "@heroui/react";
import SubmitButton from "../submit-button";
import { createResume } from "@/actions/resume";
import { createEmptyResume } from "@/lib/utils";

export default function CreateResumeButton({
  className,
  size,
}: {
  className?: string;
  size?: "lg" | "md" | "sm";
}) {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  return (
    <>
      <Button
        className={className}
        color="primary"
        radius="full"
        size={size}
        variant="shadow"
        onPress={onOpen}
      >
        Get started
      </Button>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1 justify-center items-center">
                Create Resume
              </ModalHeader>
              <ModalBody>
                <Form
                  action={(formData) => {
                    const title = formData.get("title");
                    if (!title) return;
                    createResume(createEmptyResume(title as string));
                    onClose();
                  }}
                  className="flex flex-col gap-4 p-4"
                >
                  <Input
                    label="Title"
                    name="title"
                    placeholder="Enter Resume title"
                    type="text"
                    variant="bordered"
                  />
                  <SubmitButton className="w-full self-center" size="sm">
                    Create
                  </SubmitButton>
                </Form>
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
