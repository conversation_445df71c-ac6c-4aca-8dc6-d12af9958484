"use client";

import { Checkbox, Input } from "@heroui/react";
import React from "react";
import DatePickerComponent from "../DatePicker.component";
import { TrixEditorField } from "../TrixEditorField";
import { UserPhotoUpload } from "./user-upload-photo";
import { Resume } from "@/types/resume";

const PersonalFormFields: React.FC<{ data: Resume }> = ({ data }) => {
  return (
    <div className="grid grid-cols-2 gap-4 h-full w-full">
      <div className="col-span-full my-2 flex flex-col gap-2 px-4 col-span-full">
        <UserPhotoUpload photo={data.photo} />
        <Checkbox className="" defaultChecked={data.show_photo}>
          Show photo
        </Checkbox>
      </div>

      <Input
        defaultValue={data.first_name ?? ""}
        label="First name"
        name="resume[first_name]"
      />

      <Input
        defaultValue={data.last_name ?? ""}
        label="Last name"
        name="resume[last_name]"
      />

      <Input
        defaultValue={data.job_title ?? ""}
        label="Job title"
        name="resume[job_title]"
      />

      <Input
        defaultValue={data.email ?? ""}
        label="Email"
        name="resume[email]"
        type="email"
      />

      <Input
        defaultValue={data.website ?? ""}
        label="Website"
        name="resume[website]"
      />

      {/* TODO bio */}

      <Input defaultValue={data.city ?? ""} label="City" name="resume[city]" />

      <Input
        defaultValue={data.street ?? ""}
        label="Street"
        name="resume[street]"
      />

      <Input
        defaultValue={data.country ?? ""}
        label="Country"
        name="resume[country]"
      />

      <Input
        defaultValue={data.address ?? ""}
        label="Address"
        name="resume[address]"
      />

      <DatePickerComponent
        defaultValue={data.birth_date ?? ""}
        label="Birth date"
        name="resume[birth_date]"
      />

      <TrixEditorField
        className="col-span-full"
        id="bio"
        label={"Bio"}
        name="resume[bio]"
        value={data.bio?.body ?? ""}
      />
    </div>
  );
};

export default PersonalFormFields;
