"use client";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { routes } from "@/config/path-constants";
import { formatDate, getInitials } from "@/lib/utils";
import { Resume } from "@/types/resume";

interface ResumeCardProps {
  resume: Resume;
}

export default function ResumeCard({ resume }: ResumeCardProps) {
  // Get last modified date
  const lastModified = resume.updated_at || resume.created_at;
  const dateFormatted = lastModified ? formatDate(lastModified) : "";

  return (
    <Card
      isPressable
      className="col-span-1 relative"
      shadow="sm"
      onPress={() => {}}
    >
      {/* Subtle decoration at the top of the card */}
      <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary/80 via-primary to-primary/80" />

      <CardHeader className="">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            {resume.photo ? (
              <Avatar className="h-14 w-14" src={resume.photo} />
            ) : (
              <Avatar
                className="uppercase text-lg"
                name={getInitials(resume.first_name, resume.last_name)}
              />
            )}
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-lg text-foreground truncate transition-all duration-300 group-hover:text-primary">
                {resume.title || "untitled resume"}
              </h3>
              <div className="flex items-center flex-wrap gap-2 mt-2">
                <p className="text-sm font-medium text-foreground/90">
                  {resume.first_name} {resume.last_name}
                </p>
                {resume.job_title && (
                  <Chip
                    className="text-xs px-2 py-0.5 bg-primary/5 text-primary border-primary/20 transition-colors"
                    color="primary"
                    size="sm"
                    variant="flat"
                  >
                    {resume.job_title}
                  </Chip>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardBody className="pt-0 pb-4">
        <div className="space-y-4">
          {resume.bio && (
            <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed bg-muted/30 rounded-md p-3 border border-border/20">
              {typeof resume.bio === "string" ? resume.bio : resume.bio?.body || ""}
            </p>
          )}

          <div className="flex flex-wrap gap-3 text-xs text-muted-foreground">
            {resume.email && (
              <div className="flex items-center gap-1.5 bg-background rounded-full px-2.5 py-1 shadow-sm border border-border/20">
                <Icon height="12" icon="lucide:mail" width="12" />
                <span className="truncate max-w-[120px]">{resume.email}</span>
              </div>
            )}
            {(resume.city || resume.country) && (
              <div className="flex items-center gap-1.5 bg-background rounded-full px-2.5 py-1 shadow-sm border border-border/20">
                <Icon height="12" icon="lucide:map-pin" width="12" />
                <span>
                  {[resume.city, resume.country].filter(Boolean).join(", ")}
                </span>
              </div>
            )}
            {lastModified && (
              <div className="flex items-center gap-1.5 bg-background rounded-full px-2.5 py-1 shadow-sm border border-border/20">
                <Icon height="12" icon="lucide:calendar" width="12" />
                <span>{dateFormatted}</span>
              </div>
            )}
          </div>
        </div>
      </CardBody>

      <CardFooter className="">
        <div className="flex items-center justify-end w-full">
          <div className={`flex gap-4`}>
            <Link
              color="foreground"
              href={routes.resumeEditPath(resume.id)}
              title="Edit"
            >
              <Icon height="16" icon="lucide:edit-3" width="16" />
            </Link>

            <Link
              color="foreground"
              href={`/resumes/${resume.id}/export_pdf`}
              title="Download"
            >
              <Icon height="16" icon="lucide:download" width="16" />
            </Link>
            <Button
              isIconOnly
              aria-label="Delete"
              className="outline-none border-none"
              color="danger"
              disabled={false}
              size="sm"
              variant="faded"
              onPress={() => {}}
            >
              <Icon height="12" icon="lucide:trash-2" width="12" />
            </Button>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
