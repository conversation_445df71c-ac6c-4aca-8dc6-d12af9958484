"use client";
import { Accordion, AccordionItem, Button, Form, Input } from "@heroui/react";
import { useActionState, useMemo } from "react";

import { useRouter } from "next/navigation";
import { Icon } from "@iconify/react";
import AccordionFormList from "../AccordionFormList";
import SubmitButton from "../submit-button";

import PersonalFormFields from "./personalFormFields";

import { editResume } from "@/actions/resume";
import { useActionErrors } from "@/hooks/use-action-errors";
import { prepareNestedForms, RenderForm } from "@/lib/form";
import { Resume } from "@/types/resume";
import { upperCaseFirstLetter } from "@/lib/utils";

export const ResumeEditForm = ({ data: initialData }: { data: Resume }) => {
  const [state, action] = useActionState(editResume, {
    data: initialData,
    errors: {
      fieldErrors: {},
      formErrors: [],
    },
  });

  const router = useRouter();

  const { errors, setFieldError } = useActionErrors(state);

  const fieldErrors = errors?.fieldErrors || {};
  const formErrors = errors?.formErrors;

  const resumeData = state?.data || initialData;

  // console.log(resumeData);

  const nested_forms = useMemo(
    () => prepareNestedForms(resumeData),
    [resumeData],
  );

  return (
    <div className="flex flex-col gap-4 w-full h-full">
      <Form action={action}>
        <Input
          defaultValue={resumeData?.id.toString()}
          name="id"
          type="hidden"
        />
        <PersonalFormFields data={resumeData} />
        <div className="w-full mt-4">
          {nested_forms.map((form) => {
            return (
              <div key={form.name} className="mb-4">
                <Accordion isCompact variant="splitted">
                  <AccordionItem
                    key={form.name}
                    aria-label={form.schema.collection}
                    indicator={addItemButton({
                      onAddNew: () => router.push(form.route(resumeData.id)),
                    })}
                    subtitle={form.schema.description}
                    title={upperCaseFirstLetter(form.schema.collection)}
                  >
                    <AccordionFormList
                      className="p-4"
                      keyName="id"
                      items={form.items
                        ?.filter((item) => item.id != null)
                        .map((item, index) => ({
                          ...item,
                          id: item.id!,
                          sort: index,
                        }))}
                      renderForm={(item, index) => (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <RenderForm
                            index={index}
                            item={item}
                            schema={form.schema}
                          />
                        </div>
                      )}
                      titlePrefix={form.schema.entity}
                    />
                  </AccordionItem>
                </Accordion>
              </div>
            );
          })}
        </div>
        <div className="my-4 w-full">
          <SubmitButton>Save</SubmitButton>
        </div>
      </Form>
    </div>
  );
};

const addItemButton = ({ onAddNew }: { onAddNew: () => void }) => {
  return (
    <Button
      isIconOnly
      color="primary"
      size="sm"
      startContent={<Icon icon="lucide:plus" />}
      variant="flat"
      onPress={onAddNew}
    />
  );
};
