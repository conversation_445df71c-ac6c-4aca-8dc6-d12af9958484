import { But<PERSON> } from "@heroui/button";
import { Link } from "@heroui/link";
import {
  Navbar as HeroUINavbar,
  NavbarBrand,
  NavbarContent,
  NavbarItem,
  NavbarMenu,
  NavbarMenuItem,
  NavbarMenuToggle,
} from "@heroui/navbar";
import NextLink from "next/link";

import { LogOutButton } from "./LogOutButton";
import CreateResumeButton from "./resume/createResumeButton";
import {
  GithubIcon,
  HeartFilledIcon,
  Logo,
  TwitterIcon,
} from "@/components/icons";
import { ThemeSwitch } from "@/components/theme-switch";
import { NavItemConfig, siteConfig } from "@/config/site"; // Updated import
import { isAuthenticated } from "@/lib/auth"; // New import

export const Navbar = async () => {
  const authenticated = await isAuthenticated();

  const visibleNavItems = siteConfig.navItems.filter((item: NavItemConfig) => {
    const displayCondition = item.displayCondition || "always"; // Default to "always"
    if (displayCondition === "authenticated") {
      return authenticated;
    }
    if (displayCondition === "guest") {
      return !authenticated;
    }
    return true; // "always"
  });

  const visibleNavMenuItems = siteConfig.navMenuItems.filter(
    (item: NavItemConfig) => {
      const displayCondition = item.displayCondition || "always"; // Default to "always"
      if (displayCondition === "authenticated") {
        return authenticated;
      }
      if (displayCondition === "guest") {
        return !authenticated;
      }
      return true; // "always"
    },
  );

  return (
    <HeroUINavbar maxWidth="xl" position="sticky">
      <NavbarContent className="basis-1/5 sm:basis-full" justify="start">
        <NavbarBrand as="li" className="gap-3 max-w-fit">
          <NextLink className="flex justify-start items-center gap-1" href="/">
            <Logo />
            <p className="font-bold text-inherit">ACME</p>
          </NextLink>
        </NavbarBrand>
        <ul className="hidden lg:flex gap-4 justify-start items-center ml-2">
          {visibleNavItems.map((item: NavItemConfig) => (
            <NavbarItem key={item.href}>
              {item.href.includes("logout") ||
              item.label.toLowerCase() === "logout" ? (
                <LogOutButton />
              ) : (
                <Link color="foreground" href={item.href}>
                  {item.label}
                </Link>
              )}
            </NavbarItem>
          ))}
          <CreateResumeButton size="sm" />
        </ul>
      </NavbarContent>

      <NavbarContent
        className="hidden sm:flex basis-1/5 sm:basis-full"
        justify="end"
      >
        <NavbarItem className="hidden sm:flex gap-2">
          <Link isExternal aria-label="Twitter" href={siteConfig.links.twitter}>
            <TwitterIcon className="text-default-500" />
          </Link>
          <Link isExternal aria-label="Github" href={siteConfig.links.github}>
            <GithubIcon className="text-default-500" />
          </Link>
          <ThemeSwitch />
        </NavbarItem>

        <NavbarItem className="hidden md:flex">
          <Button
            isExternal
            as={Link}
            className="text-sm font-normal text-default-600 bg-default-100"
            href={siteConfig.links.sponsor}
            startContent={<HeartFilledIcon className="text-danger" />}
            variant="flat"
          >
            Sponsor
          </Button>
        </NavbarItem>
      </NavbarContent>

      <NavbarContent className="sm:hidden basis-1 pl-4" justify="end">
        <Link isExternal aria-label="Github" href={siteConfig.links.github}>
          <GithubIcon className="text-default-500" />
        </Link>
        <ThemeSwitch />
        <NavbarMenuToggle />
      </NavbarContent>

      <NavbarMenu>
        <div className="mx-4 mt-2 flex flex-col gap-4">
          {visibleNavMenuItems.map((item: NavItemConfig, index: number) => (
            <NavbarMenuItem key={`${item.href}-${index}`}>
              {item.href.includes("logout") ||
              item.label.toLowerCase() === "logout" ? (
                <LogOutButton />
              ) : (
                <Link color="foreground" href={item.href}>
                  {item.label}
                </Link>
              )}
            </NavbarMenuItem>
          ))}
          <CreateResumeButton />
        </div>
      </NavbarMenu>
    </HeroUINavbar>
  );
};
