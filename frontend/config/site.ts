import { routes } from "./path-constants";

export type NavItemDisplayCondition = "always" | "authenticated" | "guest";

export interface NavItemConfig {
  label: string;
  href: string;
  displayCondition?: NavItemDisplayCondition; // Defaults to "always" if not specified
}

export interface SiteConfigType {
  name: string;
  description: string;
  navItems: NavItemConfig[];
  navMenuItems: NavItemConfig[]; // For the mobile/hamburger menu
  links: {
    github: string;
    twitter: string;
    docs: string;
    discord: string;
    sponsor: string;
  };
}

export const siteConfig: SiteConfigType = {
  name: "Next.js + HeroUI",
  description: "Make beautiful websites regardless of your design experience.",
  navItems: [
    // For the main navbar
    {
      label: "Home",
      href: "/",
      displayCondition: "always",
    },
    {
      label: "Resumes",
      href: routes.fetchResumesPath(),
      displayCondition: "authenticated",
    },
    {
      label: "Templates",
      href: "/templates",
    },
    {
      label: "About",
      href: "/about",
    },
    {
      label: "Logout",
      href: "/logout",
      displayCondition: "authenticated",
    },
    // Consider adding a "Logout" item here or handle it within Navbar.tsx
    // e.g., { label: "Logout", href: "/api/auth/logout", displayCondition: "authenticated" }
  ],
  navMenuItems: [
    // For the hamburger menu
    {
      label: "Profile",
      href: "/profile",
      displayCondition: "authenticated",
    },
    {
      label: "Resumes",
      href: routes.fetchResumesPath(),
      displayCondition: "authenticated",
    },
    {
      label: "Settings",
      href: "/settings",
      displayCondition: "authenticated", // Assuming settings requires authentication
    },
    {
      label: "Logout",
      href: "/logout",
      displayCondition: "authenticated",
    },
    // Consider adding a "Logout" item here for the menu
    // e.g., { label: "Logout", href: "/api/auth/logout", displayCondition: "authenticated" }
  ],
  links: {
    github: "https://github.com/heroui-inc/heroui",
    twitter: "https://twitter.com/hero_ui",
    docs: "https://heroui.com",
    discord: "https://discord.gg/9b6yyZKmH4",
    sponsor: "https://patreon.com/jrgarciadev",
  },
};

// This makes `SiteConfig` refer to the type of the `siteConfig` object
export type SiteConfig = typeof siteConfig;
