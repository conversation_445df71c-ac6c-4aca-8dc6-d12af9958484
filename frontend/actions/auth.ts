"use server";

import { redirect } from "next/navigation";
import { z } from "zod";

import { cookies } from "next/headers";
import { apiClient } from "@/lib/api";
import { createSession } from "@/lib/auth";
import { formatError, getErrorsForForm } from "@/lib/format-error";
import { ActionResult } from "@/types";

// Define Zod schema for signin validation
const SignInSchema = z.object({
  email_address: z
    .string()
    .min(1, "Email is required")
    .email("Invalid email format"),
  password: z.string().min(1, "Password is required"),
});

// Define Zod schema for signup validation
const SignUpSchema = z.object({
  email_address: z
    .string()
    .min(1, "Email is required")
    .email("Invalid email format"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

export type SignInData = z.infer<typeof SignInSchema>;
export type SignUpData = z.infer<typeof SignUpSchema>;

export type ActionResponse = {
  success: boolean;
  message: string;
  errors?: Record<string, string[]>;
  error?: string;
};

export async function signIn(prevState: ActionResult, formData: FormData) {
  try {
    const data = Object.fromEntries(formData.entries());
    const result = await SignInSchema.safeParseAsync(data);

    if (!result.success)
      return {
        errors: formatError(result.error),
      };

    const { email_address, password } = result.data;

    const response = await apiClient<{ token: string }>(`/session`, {
      method: "POST",
      body: {
        email_address,
        password,
      },
    });

    const {
      data: { token },
    } = response;

    if (!token)
      return {
        errors: getErrorsForForm("Failed to sign in"),
      };

    await createSession(token);
  } catch (error: any) {
    return {
      errors: formatError(error),
    };
  }

  redirect("/");
}

export async function signUp(state: ActionResult, formData: FormData) {
  try {
    const data = Object.fromEntries(formData.entries());
    const result = await SignUpSchema.safeParseAsync(data);
    if (!result.success)
      return {
        errors: formatError(result.error),
      };

    const { email_address, password } = result.data;

    const response = await apiClient<{ token: string }>(`/signup`, {
      method: "POST",
      body: {
        email_address,
        password,
      },
    });

    const {
      data: { token },
    } = response;

    if (!token)
      return {
        errors: getErrorsForForm("Failed to sign up"),
      };

    await createSession(token);
  } catch (error: any) {
    console.error("Sign up error:", error);

    return {
      errors: formatError(error),
    };
  }

  redirect("/");
}

export async function signOut(): Promise<void> {
  try {
    await apiClient("/session", { method: "DELETE" });
    (await cookies()).delete("auth_token");
  } catch (error) {
    console.error("Sign out error:", error);
    throw new Error("Failed to sign out");
  } finally {
    redirect("/login");
  }
}
