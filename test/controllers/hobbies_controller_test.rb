require "test_helper"

class HobbiesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @hobby = hobbies(:one)
  end

  test "should get index" do
    get hobbies_url, as: :json
    assert_response :success
  end

  test "should create hobby" do
    assert_difference("Hobby.count") do
      post hobbies_url, params: { sort: @hobby.sort, description: @hobby.description, name: @hobby.name, resume_id: @hobby.resume_id }, as: :json
    end

    assert_response :created
  end

  test "should show hobby" do
    get hobby_url(@hobby), as: :json
    assert_response :success
  end

  test "should update hobby" do
    patch hobby_url(@hobby), params: { sort: @hobby.sort, description: @hobby.description, name: @hobby.name, resume_id: @hobby.resume_id }, as: :json
    assert_response :success
  end

  test "should destroy hobby" do
    assert_difference("Hobby.count", -1) do
      delete hobby_url(@hobby), as: :json
    end

    assert_response :no_content
  end
end
