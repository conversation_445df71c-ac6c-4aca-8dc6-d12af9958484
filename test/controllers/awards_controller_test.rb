require "test_helper"

class AwardsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @award = awards(:one)
  end

  test "should get index" do
    get awards_url, as: :json
    assert_response :success
  end

  test "should create award" do
    assert_difference("Award.count") do
      post awards_url, params: { award: { date_received: @award.date_received, description: @award.description, issuer: @award.issuer, resume_id: @award.resume_id, sort: @award.sort, title: @award.title, url: @award.url } }, as: :json
    end

    assert_response :created
  end

  test "should show award" do
    get award_url(@award), as: :json
    assert_response :success
  end

  test "should update award" do
    patch award_url(@award), params: { award: { date_received: @award.date_received, description: @award.description, issuer: @award.issuer, resume_id: @award.resume_id, sort: @award.sort,   title: @award.title, url: @award.url } }, as: :json
    assert_response :success
  end

  test "should destroy award" do
    assert_difference("Award.count", -1) do
      delete award_url(@award), as: :json
    end

    assert_response :no_content
  end
end
