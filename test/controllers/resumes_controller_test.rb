require "test_helper"

class ResumesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @resume = resumes(:one)
  end

  test "should get index" do
    get resumes_url, as: :json
    assert_response :success
  end

  test "should create resume" do
    assert_difference("Resume.count") do
      post resumes_url, params: { resume: { address: @resume.address, bio: @resume.bio, birth_date: @resume.birth_date, city: @resume.city, country: @resume.country, email: @resume.email, first_name: @resume.first_name, job_title: @resume.job_title, last_name: @resume.last_name, show_photo: @resume.show_photo, street: @resume.street, title: @resume.title, user_id: @resume.user_id, website: @resume.website } }, as: :json
    end

    assert_response :created
  end

  test "should show resume" do
    get resume_url(@resume), as: :json
    assert_response :success
  end

  test "should update resume" do
    patch resume_url(@resume), params: { resume: { address: @resume.address, bio: @resume.bio, birth_date: @resume.birth_date, city: @resume.city, country: @resume.country, email: @resume.email, first_name: @resume.first_name, job_title: @resume.job_title, last_name: @resume.last_name, show_photo: @resume.show_photo, street: @resume.street, title: @resume.title, user_id: @resume.user_id, website: @resume.website } }, as: :json
    assert_response :success
  end

  test "should destroy resume" do
    assert_difference("Resume.count", -1) do
      delete resume_url(@resume), as: :json
    end

    assert_response :no_content
  end
end
