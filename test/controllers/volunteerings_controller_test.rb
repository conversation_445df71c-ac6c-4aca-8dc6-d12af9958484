require "test_helper"

class VolunteeringsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @volunteering = volunteerings(:one)
  end

  test "should get index" do
    get volunteerings_url, as: :json
    assert_response :success
  end

  test "should create volunteering" do
    assert_difference("Volunteering.count") do
      post volunteerings_url, params: { sort: @volunteering.sort, description: @volunteering.description, end_date: @volunteering.end_date, organization: @volunteering.organization, resume_id: @volunteering.resume_id, role: @volunteering.role, start_date: @volunteering.start_date }, as: :json
    end

    assert_response :created
  end

  test "should show volunteering" do
    get volunteering_url(@volunteering), as: :json
    assert_response :success
  end

  test "should update volunteering" do
    patch volunteering_url(@volunteering), params: { sort: @volunteering.sort, description: @volunteering.description, end_date: @volunteering.end_date, organization: @volunteering.organization, resume_id: @volunteering.resume_id, role: @volunteering.role, start_date: @volunteering.start_date }, as: :json
    assert_response :success
  end

  test "should destroy volunteering" do
    assert_difference("Volunteering.count", -1) do
      delete volunteering_url(@volunteering), as: :json
    end

    assert_response :no_content
  end
end
