# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  sort: 1
  title: "E-commerce Platform Overhaul"
  client: "RetailGiant Corp"
  url: "https://retailgiantcorp.com/new-platform"
  start_date: 2021-03-01
  end_date: 2022-01-31
  description: "Led the migration of a legacy e-commerce system to a modern microservices architecture using Ruby on Rails and React. Implemented new features like personalized recommendations and a streamlined checkout process. Resulted in a 25% increase in conversion rates."
  resume: one # Links to <PERSON>'s resume

alex_personal_portfolio_project:
  sort: 2
  title: "Personal Portfolio Website (alexsmith.dev)"
  client: "Personal Project"
  url: "https://alexsmith.dev"
  start_date: 2023-01-10
  end_date: 2023-03-15
  description: "Designed and developed a personal portfolio website to showcase projects, skills, and experience. Built with Next.js and deployed on Vercel. Integrated with a headless CMS for blog content."
  resume: one # Links to <PERSON>'s resume

two:  
  sort: 2
  title: MyString
  client: MyString
  url: MyString
  start_date: 2025-06-12 15:08:53
  end_date: 2025-06-12 15:08:53
  description: MyText
  resume: two
