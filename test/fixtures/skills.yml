# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  sort: 1
  name: "Ruby on Rails"
  proficiency: 90 # Assuming proficiency is 1-100
  category: "Framework"
  resume: one # Links to <PERSON>'s resume

alex_skill_ruby:
  sort: 2
  name: "<PERSON>"
  proficiency: 95
  category: "Programming Language"
  resume: one

alex_skill_javascript:
  sort: 3
  name: "JavaScript"
  proficiency: 85
  category: "Programming Language"
  resume: one

alex_skill_react:
  sort: 4
  name: "React"
  proficiency: 80
  category: "Library/Framework"
  resume: one

alex_skill_sql:
  sort: 5
  name: "SQL (PostgreSQL)"
  proficiency: 88
  category: "Database"
  resume: one

alex_skill_aws:
  sort: 6
  name: "AWS (EC2, S3, RDS)"
  proficiency: 75
  category: "Cloud Platform"
  resume: one

alex_skill_docker:
  sort: 7
  name: "Docker"
  proficiency: 70
  category: "DevOps Tool"
  resume: one

alex_skill_git:
  sort: 8
  name: "Git"
  proficiency: 90
  category: "Version Control"
  resume: one

alex_skill_agile:
  sort: 9
  name: "Agile Methodologies"
  proficiency: 90
  category: "Methodology"
  resume: one

two:
  sort: 2
  name: MyString
  proficiency: 1
  category: MyString
  resume: two
