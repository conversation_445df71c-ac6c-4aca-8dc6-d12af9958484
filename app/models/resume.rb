class Resume < ApplicationRecord
  belongs_to :user
  has_one_attached :photo
  has_rich_text :bio
  has_many :educations, dependent: :destroy
  has_many :experiences, dependent: :destroy
  has_many :projects, dependent: :destroy
  has_many :awards, dependent: :destroy
  has_many :certifications, dependent: :destroy
  has_many :skills, dependent: :destroy
  has_many :languages, dependent: :destroy
  has_many :references, dependent: :destroy
  has_many :hobbies, dependent: :destroy
  has_many :volunteerings, dependent: :destroy

  accepts_nested_attributes_for :educations, allow_destroy: true
  accepts_nested_attributes_for :experiences, allow_destroy: true
  accepts_nested_attributes_for :hobbies, allow_destroy: true
  accepts_nested_attributes_for :volunteerings, allow_destroy: true
  accepts_nested_attributes_for :projects, allow_destroy: true
  accepts_nested_attributes_for :awards, allow_destroy: true
  accepts_nested_attributes_for :certifications, allow_destroy: true
  accepts_nested_attributes_for :skills, allow_destroy: true
  accepts_nested_attributes_for :languages, allow_destroy: true
  accepts_nested_attributes_for :references, allow_destroy: true
  
 def as_json(options = {})
    super(options).merge({
      photo: photo.attached? ? Rails.application.routes.url_helpers.url_for(photo) : nil
    })
  end
end
