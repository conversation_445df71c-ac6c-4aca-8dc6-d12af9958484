class SessionsController < ApplicationController
  skip_before_action :require_authentication, only: [ :create, :signup ]
  def create
    if user = User.authenticate_by(email_address: params[:email_address], password: params[:password])
      session = user.sessions.create!(user_agent: request.user_agent, ip_address: request.remote_ip)
      render json: { token: session.token }, status: :created
    else
      render json: { error: "Invalid credentials" }, status: :unauthorized
    end
  end

  def signup
    user = User.new(email_address: params[:email_address], password: params[:password])
    if user.save
      session = user.sessions.create!(user_agent: request.user_agent, ip_address: request.remote_ip)
      render json: { token: session.token }, status: :created
    else
      render json: { errors: user.errors.full_messages }, status: :unprocessable_entity
    end
  end
  # Optional: Implement destroy action for logging out
  def destroy
    Current.session.destroy
    head :no_content
  end
  def show
    if Current.session.present?
      render json: { authenticated: true, user: Current.user }, status: :ok
    else
      render json: { error: "Not authenticated" }, status: :unauthorized
    end
  rescue StandardError => e
    render json: { error: e.message }, status: :internal_server_error
  end
end
