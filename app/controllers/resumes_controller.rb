class ResumesController < ApplicationController
  before_action :require_authentication
  before_action :set_resume, only: %i[ show update destroy ]

  # GET /resumes
  def index
    @resumes = Current.user.resumes
    render json: @resumes
  end

  # GET /resumes/1
  def show
    render json: @resume.as_json(include: [
      :educations, :experiences, :projects, :awards, :certifications, :skills, :languages, :references, :hobbies, :volunteerings
    ])
  end

  # POST /resumes
  def create
    @resume = Current.user.resumes.new(resume_params)

    if @resume.save
      render json: @resume, status: :created, location: @resume
    else
      render json: @resume.errors, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /resumes/1
  def update
    if @resume.update(resume_params)
      if params[:delete_photo] == "true"
        @resume.photo.purge
      end

      render json: @resume
    else
      render json: @resume.errors, status: :unprocessable_entity
    end
  end

  # DELETE /resumes/1
  def destroy
    @resume.destroy!
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_resume
      @resume = Current.user.resumes.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def resume_params
      params.require(:resume).permit(
        :photo, :title, :first_name, :last_name, :job_title, :address, :email, :website, :bio, :birth_date, :city, :street, :country, :show_photo, :template_id,
        educations_attributes: [ :id, :institution, :degree, :field_of_study, :start_date, :end_date, :description, :city, :country, :is_current, :website, :sort, :_destroy ],
        experiences_attributes: [ :id, :company, :title, :start_date, :end_date, :description, :city, :country, :is_current, :sort, :_destroy ],
        projects_attributes: [ :id, :title, :description, :start_date, :end_date, :url, :client, :sort, :_destroy ],
        awards_attributes: [ :id, :title, :issuer, :date, :description, :_destroy, :sort ],
        certifications_attributes: [ :id, :name, :authority, :license_number, :url, :start_date, :end_date, :_destroy, :sort ],
        skills_attributes: [ :id, :name, :level, :_destroy, :sort ],
        languages_attributes: [ :id, :name, :proficiency, :_destroy, :sort ],
        references_attributes: [ :id, :name, :contact, :relationship, :_destroy, :sort ],
        hobbies_attributes: [ :id, :name, :_destroy, :sort ],
        volunteerings_attributes: [ :id, :organization, :role, :start_date, :end_date, :description, :_destroy, :sort ]
      )
    end
end
