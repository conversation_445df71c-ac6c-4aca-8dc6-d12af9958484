class VolunteeringsController < ApplicationController
  before_action :set_volunteering, only: %i[ show update destroy ]

  # GET /volunteerings
  def index
    @volunteerings = Volunteering.all

    render json: @volunteerings
  end

  # GET /volunteerings/1
  def show
    render json: @volunteering
  end

  # POST /volunteerings
  def create
    @volunteering = Volunteering.new(volunteering_params)

    if @volunteering.save
      render json: @volunteering, status: :created, location: @volunteering
    else
      render json: @volunteering.errors, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /volunteerings/1
  def update
    if @volunteering.update(volunteering_params)
      render json: @volunteering
    else
      render json: @volunteering.errors, status: :unprocessable_entity
    end
  end

  # DELETE /volunteerings/1
  def destroy
    @volunteering.destroy!
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_volunteering
      @volunteering = Volunteering.find(params.expect(:id))
    end

    # Only allow a list of trusted parameters through.
    def volunteering_params
      params.expect(:organization, :role, :start_date, :end_date, :description, :resume_id, :sort)
    end
end
