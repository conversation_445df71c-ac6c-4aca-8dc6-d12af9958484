class CreateResumes < ActiveRecord::Migration[8.0]
  def change
    create_table :resumes do |t|
      t.string :title
      t.string :first_name
      t.string :last_name
      t.string :job_title
      t.string :address
      t.string :email
      t.string :website
      t.text :bio
      t.datetime :birth_date
      t.string :city
      t.string :street
      t.string :country
      t.boolean :show_photo
      t.references :user, null: false, foreign_key: true
      t.integer :template_id, default: 1

      t.timestamps
    end
  end
end
